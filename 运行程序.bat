@echo off
chcp 65001 >nul
title 华为荣耀设备保修查询工具

echo ========================================
echo 华为荣耀设备保修查询工具
echo ========================================
echo.
echo 正在启动程序...
echo.
echo 注意事项：
echo 1. 首次运行需要网络连接
echo 2. 会自动安装浏览器组件
echo 3. 安装过程可能需要几分钟
echo 4. 请耐心等待安装完成
echo.

cd /d "%~dp0"
if exist "dist\华为荣耀设备保修查询工具.exe" (
    start "" "dist\华为荣耀设备保修查询工具.exe"
    echo 程序已启动！
) else (
    echo 错误：找不到程序文件
    echo 请确保 dist\华为荣耀设备保修查询工具.exe 文件存在
    pause
)

timeout /t 3 >nul
