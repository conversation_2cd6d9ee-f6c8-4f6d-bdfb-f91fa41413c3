#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为/荣耀设备保修查询工具 - 打包脚本
自动化打包流程，生成单文件exe程序
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """检查打包所需的依赖"""
    print("=" * 60)
    print("检查打包环境...")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    # 检查必要文件
    required_files = [
        'honor_warranty_app.py',
        'honor_warranty_querier.py', 
        'huawei_new2_update_3.py',
        'disclaimer_txt.py',
        'honor_logo.ico',
        'honor_logo.png',
        'warning_icon.png',
        'VC_redist.x64.exe',
        'requirements.txt',
        'honor_warranty_app.spec'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 错误: 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件检查通过")
    return True

def install_dependencies():
    """安装打包依赖"""
    print("\n" + "=" * 60)
    print("安装打包依赖...")
    print("=" * 60)
    
    try:
        # 升级pip
        print("升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        print("安装项目依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        
        print("✅ 依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def clean_build_dirs():
    """清理之前的构建目录"""
    print("\n" + "=" * 60)
    print("清理构建目录...")
    print("=" * 60)
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"删除目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))
    
    print("✅ 清理完成")

def build_executable():
    """使用PyInstaller构建可执行文件"""
    print("\n" + "=" * 60)
    print("开始构建可执行文件...")
    print("=" * 60)
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "honor_warranty_app.spec"]
        
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("✅ 构建完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败:")
        print(f"返回码: {e.returncode}")
        print(f"错误输出: {e.stderr}")
        return False

def verify_build():
    """验证构建结果"""
    print("\n" + "=" * 60)
    print("验证构建结果...")
    print("=" * 60)
    
    exe_path = Path("dist/华为荣耀设备保修查询工具.exe")
    
    if not exe_path.exists():
        print("❌ 可执行文件未找到")
        return False
    
    file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
    print(f"✅ 可执行文件已生成: {exe_path}")
    print(f"文件大小: {file_size:.1f} MB")
    
    return True

def main():
    """主函数"""
    print("华为/荣耀设备保修查询工具 - 自动打包脚本")
    print("版本: 1.0")
    print("作者: Augment Agent")
    
    # 检查环境
    if not check_requirements():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return False
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败")
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建可执行文件
    if not build_executable():
        print("\n❌ 构建失败")
        return False
    
    # 验证构建结果
    if not verify_build():
        print("\n❌ 构建验证失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 打包完成!")
    print("=" * 60)
    print("可执行文件位置: dist/华为荣耀设备保修查询工具.exe")
    print("\n注意事项:")
    print("1. 首次运行时会自动下载安装Playwright和Chromium浏览器")
    print("2. 需要网络连接来下载浏览器组件")
    print("3. 可能需要管理员权限来安装VC++ Redistributable")
    print("4. 建议在目标机器上测试运行")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
    
    input("\n按回车键退出...")
