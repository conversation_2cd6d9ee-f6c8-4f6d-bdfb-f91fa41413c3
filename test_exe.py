#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为/荣耀设备保修查询工具 - exe文件测试脚本
验证打包后的exe文件是否正常工作
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_exe_file():
    """测试exe文件"""
    print("=" * 60)
    print("华为/荣耀设备保修查询工具 - exe文件测试")
    print("=" * 60)
    
    # 检查exe文件是否存在
    exe_path = Path("dist/华为荣耀设备保修查询工具.exe")
    
    if not exe_path.exists():
        print("❌ 错误: exe文件不存在")
        print(f"预期路径: {exe_path.absolute()}")
        return False
    
    # 显示文件信息
    file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
    print(f"✅ exe文件已找到: {exe_path}")
    print(f"文件大小: {file_size:.1f} MB")
    print(f"文件路径: {exe_path.absolute()}")
    
    # 检查文件是否可执行
    if not os.access(exe_path, os.X_OK):
        print("❌ 警告: 文件可能没有执行权限")
    else:
        print("✅ 文件具有执行权限")
    
    return True

def create_test_data():
    """创建测试数据文件"""
    print("\n" + "=" * 60)
    print("创建测试数据...")
    print("=" * 60)
    
    # 创建测试用的Excel文件
    try:
        import pandas as pd
        
        # 创建测试数据
        test_data = {
            '序列号': [
                'TEST123456789',
                'TEST987654321', 
                'TEST555666777'
            ],
            '备注': [
                '测试设备1',
                '测试设备2',
                '测试设备3'
            ]
        }
        
        df = pd.DataFrame(test_data)
        test_file = 'test_serial_numbers.xlsx'
        df.to_excel(test_file, index=False)
        
        print(f"✅ 测试Excel文件已创建: {test_file}")
        print(f"包含 {len(test_data['序列号'])} 个测试序列号")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("使用说明")
    print("=" * 60)
    
    print("1. 运行exe文件:")
    print("   双击 'dist/华为荣耀设备保修查询工具.exe'")
    print()
    print("2. 首次运行:")
    print("   - 程序会自动检测并安装Playwright和Chromium浏览器")
    print("   - 显示安装进度对话框")
    print("   - 需要网络连接")
    print("   - 可能需要几分钟时间")
    print()
    print("3. 功能测试:")
    print("   - 测试荣耀设备查询功能")
    print("   - 测试华为设备查询功能") 
    print("   - 测试Excel文件导入")
    print("   - 测试代理设置")
    print("   - 测试并发查询")
    print()
    print("4. 注意事项:")
    print("   - 确保有稳定的网络连接")
    print("   - 首次运行可能需要管理员权限")
    print("   - 建议在不同的Windows系统上测试")
    print()
    print("5. 测试数据:")
    print("   - 已创建 'test_serial_numbers.xlsx' 用于测试")
    print("   - 包含示例序列号数据")

def check_dependencies():
    """检查运行环境"""
    print("\n" + "=" * 60)
    print("检查运行环境...")
    print("=" * 60)
    
    # 检查操作系统
    import platform
    os_info = platform.platform()
    print(f"操作系统: {os_info}")
    
    # 检查Python版本
    python_version = sys.version
    print(f"Python版本: {python_version}")
    
    # 检查可用磁盘空间
    import shutil
    total, used, free = shutil.disk_usage(".")
    free_gb = free / (1024**3)
    print(f"可用磁盘空间: {free_gb:.1f} GB")
    
    if free_gb < 1:
        print("⚠️  警告: 磁盘空间不足，可能影响浏览器组件下载")
    
    return True

def main():
    """主函数"""
    print("华为/荣耀设备保修查询工具 - exe文件测试脚本")
    print("版本: 1.0")
    
    # 检查运行环境
    check_dependencies()
    
    # 测试exe文件
    if not test_exe_file():
        print("\n❌ exe文件测试失败")
        return False
    
    # 创建测试数据
    create_test_data()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")
    print("=" * 60)
    print("exe文件已成功生成并通过基本检查")
    print("现在可以运行exe文件进行功能测试")
    
    # 询问是否立即运行exe文件
    print("\n是否立即运行exe文件进行测试? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是', 'Y']:
            print("\n正在启动exe文件...")
            exe_path = Path("dist/华为荣耀设备保修查询工具.exe")
            subprocess.Popen([str(exe_path)], shell=True)
            print("exe文件已启动，请在新窗口中测试功能")
        else:
            print("跳过运行测试")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
    
    print("\n按回车键退出...")
    try:
        input()
    except KeyboardInterrupt:
        pass
