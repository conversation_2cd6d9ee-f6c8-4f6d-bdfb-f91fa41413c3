#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试exe文件
"""

import os
import sys
from pathlib import Path

def test_exe():
    """测试exe文件"""
    exe_path = Path("dist/华为荣耀设备保修查询工具.exe")
    
    print("=" * 50)
    print("快速测试exe文件")
    print("=" * 50)
    
    if not exe_path.exists():
        print("❌ exe文件不存在")
        return False
    
    # 获取文件信息
    stat = exe_path.stat()
    file_size_mb = stat.st_size / (1024 * 1024)
    
    print(f"✅ exe文件存在: {exe_path}")
    print(f"文件大小: {file_size_mb:.1f} MB")
    print(f"修改时间: {stat.st_mtime}")
    
    # 检查文件是否可执行
    if os.access(exe_path, os.X_OK):
        print("✅ 文件具有执行权限")
    else:
        print("❌ 文件没有执行权限")
    
    print("\n测试完成！")
    print("可以手动双击exe文件进行功能测试")
    
    return True

if __name__ == "__main__":
    test_exe()
    input("按回车键退出...")
