('E:\\env\\honor_exe\\build\\华为荣耀设备保修查询工具\\PYZ-00.pyz',
 [('IPython',
   'E:\\python\\Lib\\site-packages\\IPython\\__init__.py',
   'PYMODULE'),
  ('IPython.core',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\__init__.py',
   'PYMODULE'),
  ('IPython.core.alias',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\alias.py',
   'PYMODULE'),
  ('IPython.core.application',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\application.py',
   'PYMODULE'),
  ('IPython.core.async_helpers',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\async_helpers.py',
   'PYMODULE'),
  ('IPython.core.autocall',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\autocall.py',
   'PYMODULE'),
  ('IPython.core.builtin_trap',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\builtin_trap.py',
   'PYMODULE'),
  ('IPython.core.compilerop',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\compilerop.py',
   'PYMODULE'),
  ('IPython.core.completer',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\completer.py',
   'PYMODULE'),
  ('IPython.core.completerlib',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\completerlib.py',
   'PYMODULE'),
  ('IPython.core.crashhandler',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\crashhandler.py',
   'PYMODULE'),
  ('IPython.core.debugger',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\debugger.py',
   'PYMODULE'),
  ('IPython.core.display',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\display.py',
   'PYMODULE'),
  ('IPython.core.display_functions',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\display_functions.py',
   'PYMODULE'),
  ('IPython.core.display_trap',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\display_trap.py',
   'PYMODULE'),
  ('IPython.core.displayhook',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\displayhook.py',
   'PYMODULE'),
  ('IPython.core.displaypub',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\displaypub.py',
   'PYMODULE'),
  ('IPython.core.error',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\error.py',
   'PYMODULE'),
  ('IPython.core.events',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\events.py',
   'PYMODULE'),
  ('IPython.core.excolors',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\excolors.py',
   'PYMODULE'),
  ('IPython.core.extensions',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\extensions.py',
   'PYMODULE'),
  ('IPython.core.formatters',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\formatters.py',
   'PYMODULE'),
  ('IPython.core.getipython',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\getipython.py',
   'PYMODULE'),
  ('IPython.core.guarded_eval',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\guarded_eval.py',
   'PYMODULE'),
  ('IPython.core.history',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\history.py',
   'PYMODULE'),
  ('IPython.core.hooks',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\hooks.py',
   'PYMODULE'),
  ('IPython.core.inputtransformer2',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\inputtransformer2.py',
   'PYMODULE'),
  ('IPython.core.interactiveshell',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.core.latex_symbols',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\latex_symbols.py',
   'PYMODULE'),
  ('IPython.core.logger',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\logger.py',
   'PYMODULE'),
  ('IPython.core.macro',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\macro.py',
   'PYMODULE'),
  ('IPython.core.magic',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magic.py',
   'PYMODULE'),
  ('IPython.core.magic_arguments',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magic_arguments.py',
   'PYMODULE'),
  ('IPython.core.magics',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\__init__.py',
   'PYMODULE'),
  ('IPython.core.magics.ast_mod',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\ast_mod.py',
   'PYMODULE'),
  ('IPython.core.magics.auto',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\auto.py',
   'PYMODULE'),
  ('IPython.core.magics.basic',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\basic.py',
   'PYMODULE'),
  ('IPython.core.magics.code',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\code.py',
   'PYMODULE'),
  ('IPython.core.magics.config',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\config.py',
   'PYMODULE'),
  ('IPython.core.magics.display',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\display.py',
   'PYMODULE'),
  ('IPython.core.magics.execution',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\execution.py',
   'PYMODULE'),
  ('IPython.core.magics.extension',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\extension.py',
   'PYMODULE'),
  ('IPython.core.magics.history',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\history.py',
   'PYMODULE'),
  ('IPython.core.magics.logging',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\logging.py',
   'PYMODULE'),
  ('IPython.core.magics.namespace',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\namespace.py',
   'PYMODULE'),
  ('IPython.core.magics.osm',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\osm.py',
   'PYMODULE'),
  ('IPython.core.magics.packaging',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\packaging.py',
   'PYMODULE'),
  ('IPython.core.magics.pylab',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\pylab.py',
   'PYMODULE'),
  ('IPython.core.magics.script',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\magics\\script.py',
   'PYMODULE'),
  ('IPython.core.oinspect',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\oinspect.py',
   'PYMODULE'),
  ('IPython.core.page',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\page.py',
   'PYMODULE'),
  ('IPython.core.payload',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\payload.py',
   'PYMODULE'),
  ('IPython.core.prefilter',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\prefilter.py',
   'PYMODULE'),
  ('IPython.core.profiledir',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\profiledir.py',
   'PYMODULE'),
  ('IPython.core.pylabtools',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\pylabtools.py',
   'PYMODULE'),
  ('IPython.core.release',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\release.py',
   'PYMODULE'),
  ('IPython.core.shellapp',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\shellapp.py',
   'PYMODULE'),
  ('IPython.core.splitinput',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\splitinput.py',
   'PYMODULE'),
  ('IPython.core.ultratb',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\ultratb.py',
   'PYMODULE'),
  ('IPython.core.usage',
   'E:\\python\\Lib\\site-packages\\IPython\\core\\usage.py',
   'PYMODULE'),
  ('IPython.display',
   'E:\\python\\Lib\\site-packages\\IPython\\display.py',
   'PYMODULE'),
  ('IPython.extensions',
   'E:\\python\\Lib\\site-packages\\IPython\\extensions\\__init__.py',
   'PYMODULE'),
  ('IPython.extensions.storemagic',
   'E:\\python\\Lib\\site-packages\\IPython\\extensions\\storemagic.py',
   'PYMODULE'),
  ('IPython.external',
   'E:\\python\\Lib\\site-packages\\IPython\\external\\__init__.py',
   'PYMODULE'),
  ('IPython.external.qt_for_kernel',
   'E:\\python\\Lib\\site-packages\\IPython\\external\\qt_for_kernel.py',
   'PYMODULE'),
  ('IPython.external.qt_loaders',
   'E:\\python\\Lib\\site-packages\\IPython\\external\\qt_loaders.py',
   'PYMODULE'),
  ('IPython.lib',
   'E:\\python\\Lib\\site-packages\\IPython\\lib\\__init__.py',
   'PYMODULE'),
  ('IPython.lib.clipboard',
   'E:\\python\\Lib\\site-packages\\IPython\\lib\\clipboard.py',
   'PYMODULE'),
  ('IPython.lib.display',
   'E:\\python\\Lib\\site-packages\\IPython\\lib\\display.py',
   'PYMODULE'),
  ('IPython.lib.pretty',
   'E:\\python\\Lib\\site-packages\\IPython\\lib\\pretty.py',
   'PYMODULE'),
  ('IPython.paths',
   'E:\\python\\Lib\\site-packages\\IPython\\paths.py',
   'PYMODULE'),
  ('IPython.terminal',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.debugger',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\debugger.py',
   'PYMODULE'),
  ('IPython.terminal.embed',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\embed.py',
   'PYMODULE'),
  ('IPython.terminal.interactiveshell',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.terminal.ipapp',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\ipapp.py',
   'PYMODULE'),
  ('IPython.terminal.magics',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\magics.py',
   'PYMODULE'),
  ('IPython.terminal.prompts',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\prompts.py',
   'PYMODULE'),
  ('IPython.terminal.pt_inputhooks',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\pt_inputhooks\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.ptutils',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\ptutils.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_match',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_suggest',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.filters',
   'E:\\python\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\filters.py',
   'PYMODULE'),
  ('IPython.testing',
   'E:\\python\\Lib\\site-packages\\IPython\\testing\\__init__.py',
   'PYMODULE'),
  ('IPython.testing.skipdoctest',
   'E:\\python\\Lib\\site-packages\\IPython\\testing\\skipdoctest.py',
   'PYMODULE'),
  ('IPython.utils',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\__init__.py',
   'PYMODULE'),
  ('IPython.utils.PyColorize',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\PyColorize.py',
   'PYMODULE'),
  ('IPython.utils._process_cli',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\_process_cli.py',
   'PYMODULE'),
  ('IPython.utils._process_common',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\_process_common.py',
   'PYMODULE'),
  ('IPython.utils._process_emscripten',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\_process_emscripten.py',
   'PYMODULE'),
  ('IPython.utils._process_posix',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\_process_posix.py',
   'PYMODULE'),
  ('IPython.utils._process_win32',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\_process_win32.py',
   'PYMODULE'),
  ('IPython.utils._sysinfo',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\_sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.capture',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\capture.py',
   'PYMODULE'),
  ('IPython.utils.colorable',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\colorable.py',
   'PYMODULE'),
  ('IPython.utils.coloransi',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\coloransi.py',
   'PYMODULE'),
  ('IPython.utils.contexts',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\contexts.py',
   'PYMODULE'),
  ('IPython.utils.data',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\data.py',
   'PYMODULE'),
  ('IPython.utils.decorators',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\decorators.py',
   'PYMODULE'),
  ('IPython.utils.dir2',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\dir2.py',
   'PYMODULE'),
  ('IPython.utils.docs',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\docs.py',
   'PYMODULE'),
  ('IPython.utils.encoding',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\encoding.py',
   'PYMODULE'),
  ('IPython.utils.frame',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\frame.py',
   'PYMODULE'),
  ('IPython.utils.generics',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\generics.py',
   'PYMODULE'),
  ('IPython.utils.importstring',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\importstring.py',
   'PYMODULE'),
  ('IPython.utils.io',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\io.py',
   'PYMODULE'),
  ('IPython.utils.ipstruct',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\ipstruct.py',
   'PYMODULE'),
  ('IPython.utils.module_paths',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\module_paths.py',
   'PYMODULE'),
  ('IPython.utils.openpy',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\openpy.py',
   'PYMODULE'),
  ('IPython.utils.path',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\path.py',
   'PYMODULE'),
  ('IPython.utils.process',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\process.py',
   'PYMODULE'),
  ('IPython.utils.py3compat',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\py3compat.py',
   'PYMODULE'),
  ('IPython.utils.sentinel',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\sentinel.py',
   'PYMODULE'),
  ('IPython.utils.strdispatch',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\strdispatch.py',
   'PYMODULE'),
  ('IPython.utils.sysinfo',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.syspathcontext',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\syspathcontext.py',
   'PYMODULE'),
  ('IPython.utils.terminal',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\terminal.py',
   'PYMODULE'),
  ('IPython.utils.text',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\text.py',
   'PYMODULE'),
  ('IPython.utils.timing',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\timing.py',
   'PYMODULE'),
  ('IPython.utils.tokenutil',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\tokenutil.py',
   'PYMODULE'),
  ('IPython.utils.wildcard',
   'E:\\python\\Lib\\site-packages\\IPython\\utils\\wildcard.py',
   'PYMODULE'),
  ('PIL', 'E:\\python\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'E:\\python\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'E:\\python\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'E:\\python\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'E:\\python\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'E:\\python\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'E:\\python\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'E:\\python\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'E:\\python\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'E:\\python\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'E:\\python\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'E:\\python\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'E:\\python\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'E:\\python\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'E:\\python\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'E:\\python\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util', 'E:\\python\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'E:\\python\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'E:\\python\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt6', 'E:\\python\\Lib\\site-packages\\PyQt6\\__init__.py', 'PYMODULE'),
  ('__future__', 'E:\\python\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'E:\\python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_black_version',
   'E:\\python\\Lib\\site-packages\\_black_version.py',
   'PYMODULE'),
  ('_compat_pickle', 'E:\\python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'E:\\python\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'E:\\python\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\python\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'E:\\python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'E:\\python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins', 'E:\\python\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'E:\\python\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'E:\\python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('aiohappyeyeballs',
   'E:\\python\\Lib\\site-packages\\aiohappyeyeballs\\__init__.py',
   'PYMODULE'),
  ('aiohappyeyeballs._staggered',
   'E:\\python\\Lib\\site-packages\\aiohappyeyeballs\\_staggered.py',
   'PYMODULE'),
  ('aiohappyeyeballs.impl',
   'E:\\python\\Lib\\site-packages\\aiohappyeyeballs\\impl.py',
   'PYMODULE'),
  ('aiohappyeyeballs.types',
   'E:\\python\\Lib\\site-packages\\aiohappyeyeballs\\types.py',
   'PYMODULE'),
  ('aiohappyeyeballs.utils',
   'E:\\python\\Lib\\site-packages\\aiohappyeyeballs\\utils.py',
   'PYMODULE'),
  ('aiohttp',
   'E:\\python\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket',
   'E:\\python\\Lib\\site-packages\\aiohttp\\_websocket\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket.helpers',
   'E:\\python\\Lib\\site-packages\\aiohttp\\_websocket\\helpers.py',
   'PYMODULE'),
  ('aiohttp._websocket.models',
   'E:\\python\\Lib\\site-packages\\aiohttp\\_websocket\\models.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader',
   'E:\\python\\Lib\\site-packages\\aiohttp\\_websocket\\reader.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader_py',
   'E:\\python\\Lib\\site-packages\\aiohttp\\_websocket\\reader_py.py',
   'PYMODULE'),
  ('aiohttp._websocket.writer',
   'E:\\python\\Lib\\site-packages\\aiohttp\\_websocket\\writer.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'E:\\python\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'E:\\python\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.client',
   'E:\\python\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'E:\\python\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_middleware_digest_auth',
   'E:\\python\\Lib\\site-packages\\aiohttp\\client_middleware_digest_auth.py',
   'PYMODULE'),
  ('aiohttp.client_middlewares',
   'E:\\python\\Lib\\site-packages\\aiohttp\\client_middlewares.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'E:\\python\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'E:\\python\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'E:\\python\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'E:\\python\\Lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'E:\\python\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'E:\\python\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'E:\\python\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'E:\\python\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'E:\\python\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.http',
   'E:\\python\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'E:\\python\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'E:\\python\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'E:\\python\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'E:\\python\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.log',
   'E:\\python\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'E:\\python\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'E:\\python\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'E:\\python\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'E:\\python\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'E:\\python\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'E:\\python\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'E:\\python\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'E:\\python\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('aiohttp.web',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'E:\\python\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'E:\\python\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiosignal',
   'E:\\python\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('argparse', 'E:\\python\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'E:\\python\\Lib\\ast.py', 'PYMODULE'),
  ('asttokens',
   'E:\\python\\Lib\\site-packages\\asttokens\\__init__.py',
   'PYMODULE'),
  ('asttokens.astroid_compat',
   'E:\\python\\Lib\\site-packages\\asttokens\\astroid_compat.py',
   'PYMODULE'),
  ('asttokens.asttokens',
   'E:\\python\\Lib\\site-packages\\asttokens\\asttokens.py',
   'PYMODULE'),
  ('asttokens.line_numbers',
   'E:\\python\\Lib\\site-packages\\asttokens\\line_numbers.py',
   'PYMODULE'),
  ('asttokens.mark_tokens',
   'E:\\python\\Lib\\site-packages\\asttokens\\mark_tokens.py',
   'PYMODULE'),
  ('asttokens.util',
   'E:\\python\\Lib\\site-packages\\asttokens\\util.py',
   'PYMODULE'),
  ('asyncio', 'E:\\python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'E:\\python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'E:\\python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'E:\\python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'E:\\python\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'E:\\python\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'E:\\python\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'E:\\python\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'E:\\python\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'E:\\python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'E:\\python\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'E:\\python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'E:\\python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'E:\\python\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'E:\\python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'E:\\python\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'E:\\python\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'E:\\python\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'E:\\python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'E:\\python\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'E:\\python\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'E:\\python\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'E:\\python\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'E:\\python\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.tasks', 'E:\\python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'E:\\python\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'E:\\python\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports', 'E:\\python\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'E:\\python\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'E:\\python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'E:\\python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'E:\\python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr', 'E:\\python\\Lib\\site-packages\\attr\\__init__.py', 'PYMODULE'),
  ('attr._cmp', 'E:\\python\\Lib\\site-packages\\attr\\_cmp.py', 'PYMODULE'),
  ('attr._compat',
   'E:\\python\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'E:\\python\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'E:\\python\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make', 'E:\\python\\Lib\\site-packages\\attr\\_make.py', 'PYMODULE'),
  ('attr._next_gen',
   'E:\\python\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'E:\\python\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'E:\\python\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'E:\\python\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'E:\\python\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'E:\\python\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'E:\\python\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('backports',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'E:\\python\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt', 'E:\\python\\Lib\\site-packages\\bcrypt\\__init__.py', 'PYMODULE'),
  ('bdb', 'E:\\python\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'E:\\python\\Lib\\bisect.py', 'PYMODULE'),
  ('black.__main__',
   'E:\\python\\Lib\\site-packages\\black\\__main__.py',
   'PYMODULE'),
  ('black.concurrency',
   'E:\\python\\Lib\\site-packages\\black\\concurrency.py',
   'PYMODULE'),
  ('black.debug',
   'E:\\python\\Lib\\site-packages\\black\\debug.py',
   'PYMODULE'),
  ('black.files',
   'E:\\python\\Lib\\site-packages\\black\\files.py',
   'PYMODULE'),
  ('black.output',
   'E:\\python\\Lib\\site-packages\\black\\output.py',
   'PYMODULE'),
  ('black.report',
   'E:\\python\\Lib\\site-packages\\black\\report.py',
   'PYMODULE'),
  ('blib2to3',
   'E:\\python\\Lib\\site-packages\\blib2to3\\__init__.py',
   'PYMODULE'),
  ('blib2to3.pgen2',
   'E:\\python\\Lib\\site-packages\\blib2to3\\pgen2\\__init__.py',
   'PYMODULE'),
  ('bz2', 'E:\\python\\Lib\\bz2.py', 'PYMODULE'),
  ('cProfile', 'E:\\python\\Lib\\cProfile.py', 'PYMODULE'),
  ('calendar', 'E:\\python\\Lib\\calendar.py', 'PYMODULE'),
  ('cgi', 'E:\\python\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'E:\\python\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'E:\\python\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'E:\\python\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'E:\\python\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'E:\\python\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'E:\\python\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'E:\\python\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'E:\\python\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'E:\\python\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'E:\\python\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'E:\\python\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'E:\\python\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'E:\\python\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'E:\\python\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'E:\\python\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'E:\\python\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'E:\\python\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'E:\\python\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'E:\\python\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'E:\\python\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'E:\\python\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'E:\\python\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'E:\\python\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'E:\\python\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'E:\\python\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'E:\\python\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'E:\\python\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'E:\\python\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'E:\\python\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'E:\\python\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'E:\\python\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'E:\\python\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'E:\\python\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'E:\\python\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'E:\\python\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'E:\\python\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'E:\\python\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'E:\\python\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'E:\\python\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'E:\\python\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'E:\\python\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'E:\\python\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'E:\\python\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\python\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\python\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\python\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\python\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\python\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\python\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\python\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\python\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click', 'E:\\python\\Lib\\site-packages\\click\\__init__.py', 'PYMODULE'),
  ('click._compat',
   'E:\\python\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'E:\\python\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'E:\\python\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'E:\\python\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core', 'E:\\python\\Lib\\site-packages\\click\\core.py', 'PYMODULE'),
  ('click.decorators',
   'E:\\python\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'E:\\python\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'E:\\python\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'E:\\python\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'E:\\python\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'E:\\python\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'E:\\python\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.types',
   'E:\\python\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'E:\\python\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'E:\\python\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'E:\\python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'E:\\python\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'E:\\python\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'E:\\python\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'E:\\python\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'E:\\python\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'E:\\python\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'E:\\python\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'E:\\python\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'E:\\python\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'E:\\python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'E:\\python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'E:\\python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'E:\\python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'E:\\python\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'E:\\python\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'E:\\python\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'E:\\python\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'E:\\python\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\python\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\python\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\python\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\python\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('csv', 'E:\\python\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'E:\\python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'E:\\python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'E:\\python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'E:\\python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'E:\\python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'E:\\python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'E:\\python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'E:\\python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'E:\\python\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'E:\\python\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'E:\\python\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'E:\\python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'E:\\python\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'E:\\python\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'E:\\python\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'E:\\python\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'E:\\python\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'E:\\python\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'E:\\python\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'E:\\python\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'E:\\python\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'E:\\python\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'E:\\python\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'E:\\python\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'E:\\python\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'E:\\python\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'E:\\python\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'E:\\python\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('ddddocr',
   'E:\\python\\Lib\\site-packages\\ddddocr\\__init__.py',
   'PYMODULE'),
  ('decimal', 'E:\\python\\Lib\\decimal.py', 'PYMODULE'),
  ('decorator', 'E:\\python\\Lib\\site-packages\\decorator.py', 'PYMODULE'),
  ('defusedxml',
   'E:\\python\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'E:\\python\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'E:\\python\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'E:\\python\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'E:\\python\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'E:\\python\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'E:\\python\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'E:\\python\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'E:\\python\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'E:\\python\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'E:\\python\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'E:\\python\\Lib\\dis.py', 'PYMODULE'),
  ('disclaimer_txt', 'E:\\env\\honor_exe\\disclaimer_txt.py', 'PYMODULE'),
  ('doctest', 'E:\\python\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'E:\\python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'E:\\python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'E:\\python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'E:\\python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'E:\\python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'E:\\python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'E:\\python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'E:\\python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'E:\\python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'E:\\python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'E:\\python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'E:\\python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'E:\\python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'E:\\python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'E:\\python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'E:\\python\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'E:\\python\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'E:\\python\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('executing',
   'E:\\python\\Lib\\site-packages\\executing\\__init__.py',
   'PYMODULE'),
  ('executing._exceptions',
   'E:\\python\\Lib\\site-packages\\executing\\_exceptions.py',
   'PYMODULE'),
  ('executing._position_node_finder',
   'E:\\python\\Lib\\site-packages\\executing\\_position_node_finder.py',
   'PYMODULE'),
  ('executing.executing',
   'E:\\python\\Lib\\site-packages\\executing\\executing.py',
   'PYMODULE'),
  ('executing.version',
   'E:\\python\\Lib\\site-packages\\executing\\version.py',
   'PYMODULE'),
  ('filecmp', 'E:\\python\\Lib\\filecmp.py', 'PYMODULE'),
  ('fileinput', 'E:\\python\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'E:\\python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'E:\\python\\Lib\\fractions.py', 'PYMODULE'),
  ('frozenlist',
   'E:\\python\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('ftplib', 'E:\\python\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'E:\\python\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'E:\\python\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'E:\\python\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'E:\\python\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'E:\\python\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'E:\\python\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'E:\\python\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'E:\\python\\Lib\\hmac.py', 'PYMODULE'),
  ('honor_warranty_querier',
   'E:\\env\\honor_exe\\honor_warranty_querier.py',
   'PYMODULE'),
  ('html', 'E:\\python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'E:\\python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'E:\\python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'E:\\python\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'E:\\python\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'E:\\python\\Lib\\http\\server.py', 'PYMODULE'),
  ('huawei_new2_update_3',
   'E:\\env\\honor_exe\\huawei_new2_update_3.py',
   'PYMODULE'),
  ('idna', 'E:\\python\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'E:\\python\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'E:\\python\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\python\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\python\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\python\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'E:\\python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'E:\\python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'E:\\python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'E:\\python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'E:\\python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'E:\\python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'E:\\python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'E:\\python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'E:\\python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'E:\\python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'E:\\python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'E:\\python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'E:\\python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'E:\\python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'E:\\python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'E:\\python\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'E:\\python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'E:\\python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'E:\\python\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'E:\\python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jedi', 'E:\\python\\Lib\\site-packages\\jedi\\__init__.py', 'PYMODULE'),
  ('jedi._compatibility',
   'E:\\python\\Lib\\site-packages\\jedi\\_compatibility.py',
   'PYMODULE'),
  ('jedi.api',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\__init__.py',
   'PYMODULE'),
  ('jedi.api.classes',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\classes.py',
   'PYMODULE'),
  ('jedi.api.completion',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\completion.py',
   'PYMODULE'),
  ('jedi.api.completion_cache',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\completion_cache.py',
   'PYMODULE'),
  ('jedi.api.environment',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\environment.py',
   'PYMODULE'),
  ('jedi.api.errors',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\errors.py',
   'PYMODULE'),
  ('jedi.api.exceptions',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\exceptions.py',
   'PYMODULE'),
  ('jedi.api.file_name',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\file_name.py',
   'PYMODULE'),
  ('jedi.api.helpers',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\helpers.py',
   'PYMODULE'),
  ('jedi.api.interpreter',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\interpreter.py',
   'PYMODULE'),
  ('jedi.api.keywords',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\keywords.py',
   'PYMODULE'),
  ('jedi.api.project',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\project.py',
   'PYMODULE'),
  ('jedi.api.refactoring',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\refactoring\\__init__.py',
   'PYMODULE'),
  ('jedi.api.refactoring.extract',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\refactoring\\extract.py',
   'PYMODULE'),
  ('jedi.api.strings',
   'E:\\python\\Lib\\site-packages\\jedi\\api\\strings.py',
   'PYMODULE'),
  ('jedi.cache', 'E:\\python\\Lib\\site-packages\\jedi\\cache.py', 'PYMODULE'),
  ('jedi.common',
   'E:\\python\\Lib\\site-packages\\jedi\\common.py',
   'PYMODULE'),
  ('jedi.debug', 'E:\\python\\Lib\\site-packages\\jedi\\debug.py', 'PYMODULE'),
  ('jedi.file_io',
   'E:\\python\\Lib\\site-packages\\jedi\\file_io.py',
   'PYMODULE'),
  ('jedi.inference',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.analysis',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\analysis.py',
   'PYMODULE'),
  ('jedi.inference.arguments',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\arguments.py',
   'PYMODULE'),
  ('jedi.inference.base_value',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\base_value.py',
   'PYMODULE'),
  ('jedi.inference.cache',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\cache.py',
   'PYMODULE'),
  ('jedi.inference.compiled',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\compiled\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.access',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\compiled\\access.py',
   'PYMODULE'),
  ('jedi.inference.compiled.getattr_static',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\compiled\\getattr_static.py',
   'PYMODULE'),
  ('jedi.inference.compiled.mixed',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\compiled\\mixed.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess.functions',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\functions.py',
   'PYMODULE'),
  ('jedi.inference.compiled.value',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\compiled\\value.py',
   'PYMODULE'),
  ('jedi.inference.context',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\context.py',
   'PYMODULE'),
  ('jedi.inference.docstring_utils',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\docstring_utils.py',
   'PYMODULE'),
  ('jedi.inference.docstrings',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\docstrings.py',
   'PYMODULE'),
  ('jedi.inference.dynamic_params',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\dynamic_params.py',
   'PYMODULE'),
  ('jedi.inference.filters',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\filters.py',
   'PYMODULE'),
  ('jedi.inference.finder',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\finder.py',
   'PYMODULE'),
  ('jedi.inference.flow_analysis',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\flow_analysis.py',
   'PYMODULE'),
  ('jedi.inference.gradual',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.gradual.annotation',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\annotation.py',
   'PYMODULE'),
  ('jedi.inference.gradual.base',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\base.py',
   'PYMODULE'),
  ('jedi.inference.gradual.conversion',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\conversion.py',
   'PYMODULE'),
  ('jedi.inference.gradual.generics',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\generics.py',
   'PYMODULE'),
  ('jedi.inference.gradual.stub_value',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\stub_value.py',
   'PYMODULE'),
  ('jedi.inference.gradual.type_var',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\type_var.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typeshed',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\typeshed.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typing',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\typing.py',
   'PYMODULE'),
  ('jedi.inference.gradual.utils',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\gradual\\utils.py',
   'PYMODULE'),
  ('jedi.inference.helpers',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\helpers.py',
   'PYMODULE'),
  ('jedi.inference.imports',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\imports.py',
   'PYMODULE'),
  ('jedi.inference.lazy_value',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\lazy_value.py',
   'PYMODULE'),
  ('jedi.inference.names',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\names.py',
   'PYMODULE'),
  ('jedi.inference.param',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\param.py',
   'PYMODULE'),
  ('jedi.inference.parser_cache',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\parser_cache.py',
   'PYMODULE'),
  ('jedi.inference.recursion',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\recursion.py',
   'PYMODULE'),
  ('jedi.inference.references',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\references.py',
   'PYMODULE'),
  ('jedi.inference.signature',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\signature.py',
   'PYMODULE'),
  ('jedi.inference.star_args',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\star_args.py',
   'PYMODULE'),
  ('jedi.inference.syntax_tree',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\syntax_tree.py',
   'PYMODULE'),
  ('jedi.inference.sys_path',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\sys_path.py',
   'PYMODULE'),
  ('jedi.inference.utils',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\utils.py',
   'PYMODULE'),
  ('jedi.inference.value',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.value.decorator',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\decorator.py',
   'PYMODULE'),
  ('jedi.inference.value.dynamic_arrays',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\dynamic_arrays.py',
   'PYMODULE'),
  ('jedi.inference.value.function',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\function.py',
   'PYMODULE'),
  ('jedi.inference.value.instance',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\instance.py',
   'PYMODULE'),
  ('jedi.inference.value.iterable',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\iterable.py',
   'PYMODULE'),
  ('jedi.inference.value.klass',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\klass.py',
   'PYMODULE'),
  ('jedi.inference.value.module',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\module.py',
   'PYMODULE'),
  ('jedi.inference.value.namespace',
   'E:\\python\\Lib\\site-packages\\jedi\\inference\\value\\namespace.py',
   'PYMODULE'),
  ('jedi.parser_utils',
   'E:\\python\\Lib\\site-packages\\jedi\\parser_utils.py',
   'PYMODULE'),
  ('jedi.plugins',
   'E:\\python\\Lib\\site-packages\\jedi\\plugins\\__init__.py',
   'PYMODULE'),
  ('jedi.plugins.django',
   'E:\\python\\Lib\\site-packages\\jedi\\plugins\\django.py',
   'PYMODULE'),
  ('jedi.plugins.flask',
   'E:\\python\\Lib\\site-packages\\jedi\\plugins\\flask.py',
   'PYMODULE'),
  ('jedi.plugins.pytest',
   'E:\\python\\Lib\\site-packages\\jedi\\plugins\\pytest.py',
   'PYMODULE'),
  ('jedi.plugins.registry',
   'E:\\python\\Lib\\site-packages\\jedi\\plugins\\registry.py',
   'PYMODULE'),
  ('jedi.plugins.stdlib',
   'E:\\python\\Lib\\site-packages\\jedi\\plugins\\stdlib.py',
   'PYMODULE'),
  ('jedi.settings',
   'E:\\python\\Lib\\site-packages\\jedi\\settings.py',
   'PYMODULE'),
  ('json', 'E:\\python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'E:\\python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'E:\\python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'E:\\python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.config', 'E:\\python\\Lib\\logging\\config.py', 'PYMODULE'),
  ('logging.handlers', 'E:\\python\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('lxml', 'E:\\python\\Lib\\site-packages\\lxml\\__init__.py', 'PYMODULE'),
  ('lxml.ElementInclude',
   'E:\\python\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'E:\\python\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'E:\\python\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'E:\\python\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'E:\\python\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'E:\\python\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'E:\\python\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'E:\\python\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'E:\\python\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'E:\\python\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'E:\\python\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'E:\\python\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'E:\\python\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'E:\\python\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'E:\\python\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib_inline',
   'E:\\python\\Lib\\site-packages\\matplotlib_inline\\__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   'E:\\python\\Lib\\site-packages\\matplotlib_inline\\backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   'E:\\python\\Lib\\site-packages\\matplotlib_inline\\config.py',
   'PYMODULE'),
  ('mimetypes', 'E:\\python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multidict',
   'E:\\python\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._abc',
   'E:\\python\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('multidict._compat',
   'E:\\python\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'E:\\python\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multiprocessing',
   'E:\\python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'E:\\python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'E:\\python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'E:\\python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'E:\\python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'E:\\python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'E:\\python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'E:\\python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'E:\\python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'E:\\python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'E:\\python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'E:\\python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'E:\\python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'E:\\python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'E:\\python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'E:\\python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'E:\\python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'E:\\python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'E:\\python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'E:\\python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'E:\\python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('mypy_extensions',
   'E:\\python\\Lib\\site-packages\\mypy_extensions.py',
   'PYMODULE'),
  ('netrc', 'E:\\python\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'E:\\python\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy', 'E:\\python\\Lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.__config__',
   'E:\\python\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'E:\\python\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'E:\\python\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'E:\\python\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'E:\\python\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'E:\\python\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'E:\\python\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'E:\\python\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'E:\\python\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'E:\\python\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'E:\\python\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'E:\\python\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'E:\\python\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'E:\\python\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'E:\\python\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'E:\\python\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'E:\\python\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'E:\\python\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'E:\\python\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'E:\\python\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'E:\\python\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'E:\\python\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'E:\\python\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'E:\\python\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'E:\\python\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'E:\\python\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'E:\\python\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'E:\\python\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'E:\\python\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'E:\\python\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'E:\\python\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'E:\\python\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'E:\\python\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'E:\\python\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'E:\\python\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'E:\\python\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'E:\\python\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'E:\\python\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'E:\\python\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'E:\\python\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'E:\\python\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'E:\\python\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'E:\\python\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'E:\\python\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'E:\\python\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'E:\\python\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('onnxruntime',
   'E:\\python\\Lib\\site-packages\\onnxruntime\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi',
   'E:\\python\\Lib\\site-packages\\onnxruntime\\capi\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi._ld_preload',
   'E:\\python\\Lib\\site-packages\\onnxruntime\\capi\\_ld_preload.py',
   'PYMODULE'),
  ('onnxruntime.capi._pybind_state',
   'E:\\python\\Lib\\site-packages\\onnxruntime\\capi\\_pybind_state.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_collect_build_info',
   'E:\\python\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_collect_build_info.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_inference_collection',
   'E:\\python\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_inference_collection.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_validation',
   'E:\\python\\Lib\\site-packages\\onnxruntime\\capi\\onnxruntime_validation.py',
   'PYMODULE'),
  ('onnxruntime.capi.version_info',
   'E:\\python\\Lib\\site-packages\\onnxruntime\\capi\\version_info.py',
   'PYMODULE'),
  ('opcode', 'E:\\python\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'E:\\python\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'E:\\python\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'E:\\python\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'E:\\python\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'E:\\python\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'E:\\python\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'E:\\python\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'E:\\python\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'E:\\python\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'E:\\python\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'E:\\python\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'E:\\python\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'E:\\python\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'E:\\python\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'E:\\python\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'E:\\python\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'E:\\python\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'E:\\python\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'E:\\python\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'E:\\python\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'E:\\python\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'E:\\python\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'E:\\python\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'E:\\python\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'E:\\python\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'E:\\python\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'E:\\python\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'E:\\python\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'E:\\python\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'E:\\python\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'E:\\python\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'E:\\python\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'E:\\python\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'E:\\python\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'E:\\python\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'E:\\python\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'E:\\python\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'E:\\python\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'E:\\python\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'E:\\python\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'E:\\python\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'E:\\python\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'E:\\python\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'E:\\python\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'E:\\python\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'E:\\python\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'E:\\python\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'E:\\python\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'E:\\python\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'E:\\python\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'E:\\python\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'E:\\python\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'E:\\python\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'E:\\python\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'E:\\python\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'E:\\python\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'E:\\python\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'E:\\python\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'E:\\python\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'E:\\python\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'E:\\python\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\python\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\python\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\python\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\python\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\python\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\python\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\python\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\python\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\python\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'E:\\python\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\python\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\python\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\python\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\python\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\python\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas', 'E:\\python\\Lib\\site-packages\\pandas\\__init__.py', 'PYMODULE'),
  ('pandas._config',
   'E:\\python\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'E:\\python\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'E:\\python\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'E:\\python\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'E:\\python\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'E:\\python\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'E:\\python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'E:\\python\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'E:\\python\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'E:\\python\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'E:\\python\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'E:\\python\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'E:\\python\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'E:\\python\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'E:\\python\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'E:\\python\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'E:\\python\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'E:\\python\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'E:\\python\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'E:\\python\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'E:\\python\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'E:\\python\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'E:\\python\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'E:\\python\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'E:\\python\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'E:\\python\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'E:\\python\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'E:\\python\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'E:\\python\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'E:\\python\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'E:\\python\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'E:\\python\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'E:\\python\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'E:\\python\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'E:\\python\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'E:\\python\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'E:\\python\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'E:\\python\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'E:\\python\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'E:\\python\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'E:\\python\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'E:\\python\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'E:\\python\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'E:\\python\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'E:\\python\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'E:\\python\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'E:\\python\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'E:\\python\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'E:\\python\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'E:\\python\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'E:\\python\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('parso', 'E:\\python\\Lib\\site-packages\\parso\\__init__.py', 'PYMODULE'),
  ('parso._compatibility',
   'E:\\python\\Lib\\site-packages\\parso\\_compatibility.py',
   'PYMODULE'),
  ('parso.cache',
   'E:\\python\\Lib\\site-packages\\parso\\cache.py',
   'PYMODULE'),
  ('parso.file_io',
   'E:\\python\\Lib\\site-packages\\parso\\file_io.py',
   'PYMODULE'),
  ('parso.grammar',
   'E:\\python\\Lib\\site-packages\\parso\\grammar.py',
   'PYMODULE'),
  ('parso.normalizer',
   'E:\\python\\Lib\\site-packages\\parso\\normalizer.py',
   'PYMODULE'),
  ('parso.parser',
   'E:\\python\\Lib\\site-packages\\parso\\parser.py',
   'PYMODULE'),
  ('parso.pgen2',
   'E:\\python\\Lib\\site-packages\\parso\\pgen2\\__init__.py',
   'PYMODULE'),
  ('parso.pgen2.generator',
   'E:\\python\\Lib\\site-packages\\parso\\pgen2\\generator.py',
   'PYMODULE'),
  ('parso.pgen2.grammar_parser',
   'E:\\python\\Lib\\site-packages\\parso\\pgen2\\grammar_parser.py',
   'PYMODULE'),
  ('parso.python',
   'E:\\python\\Lib\\site-packages\\parso\\python\\__init__.py',
   'PYMODULE'),
  ('parso.python.diff',
   'E:\\python\\Lib\\site-packages\\parso\\python\\diff.py',
   'PYMODULE'),
  ('parso.python.errors',
   'E:\\python\\Lib\\site-packages\\parso\\python\\errors.py',
   'PYMODULE'),
  ('parso.python.parser',
   'E:\\python\\Lib\\site-packages\\parso\\python\\parser.py',
   'PYMODULE'),
  ('parso.python.pep8',
   'E:\\python\\Lib\\site-packages\\parso\\python\\pep8.py',
   'PYMODULE'),
  ('parso.python.prefix',
   'E:\\python\\Lib\\site-packages\\parso\\python\\prefix.py',
   'PYMODULE'),
  ('parso.python.token',
   'E:\\python\\Lib\\site-packages\\parso\\python\\token.py',
   'PYMODULE'),
  ('parso.python.tokenize',
   'E:\\python\\Lib\\site-packages\\parso\\python\\tokenize.py',
   'PYMODULE'),
  ('parso.python.tree',
   'E:\\python\\Lib\\site-packages\\parso\\python\\tree.py',
   'PYMODULE'),
  ('parso.tree', 'E:\\python\\Lib\\site-packages\\parso\\tree.py', 'PYMODULE'),
  ('parso.utils',
   'E:\\python\\Lib\\site-packages\\parso\\utils.py',
   'PYMODULE'),
  ('pathlib', 'E:\\python\\Lib\\pathlib.py', 'PYMODULE'),
  ('pathspec',
   'E:\\python\\Lib\\site-packages\\pathspec\\__init__.py',
   'PYMODULE'),
  ('pathspec._meta',
   'E:\\python\\Lib\\site-packages\\pathspec\\_meta.py',
   'PYMODULE'),
  ('pathspec.gitignore',
   'E:\\python\\Lib\\site-packages\\pathspec\\gitignore.py',
   'PYMODULE'),
  ('pathspec.pathspec',
   'E:\\python\\Lib\\site-packages\\pathspec\\pathspec.py',
   'PYMODULE'),
  ('pathspec.pattern',
   'E:\\python\\Lib\\site-packages\\pathspec\\pattern.py',
   'PYMODULE'),
  ('pathspec.patterns',
   'E:\\python\\Lib\\site-packages\\pathspec\\patterns\\__init__.py',
   'PYMODULE'),
  ('pathspec.patterns.gitwildmatch',
   'E:\\python\\Lib\\site-packages\\pathspec\\patterns\\gitwildmatch.py',
   'PYMODULE'),
  ('pathspec.util',
   'E:\\python\\Lib\\site-packages\\pathspec\\util.py',
   'PYMODULE'),
  ('pdb', 'E:\\python\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'E:\\python\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'E:\\python\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkg_resources',
   'E:\\python\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'E:\\python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'E:\\python\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'E:\\python\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'E:\\python\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'E:\\python\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'E:\\python\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'E:\\python\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'E:\\python\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'E:\\python\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('playwright',
   'E:\\python\\Lib\\site-packages\\playwright\\__init__.py',
   'PYMODULE'),
  ('playwright._impl',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._accessibility',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_accessibility.py',
   'PYMODULE'),
  ('playwright._impl._api_structures',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_api_structures.py',
   'PYMODULE'),
  ('playwright._impl._artifact',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_artifact.py',
   'PYMODULE'),
  ('playwright._impl._assertions',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_assertions.py',
   'PYMODULE'),
  ('playwright._impl._async_base',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_async_base.py',
   'PYMODULE'),
  ('playwright._impl._browser',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_browser.py',
   'PYMODULE'),
  ('playwright._impl._browser_context',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_browser_context.py',
   'PYMODULE'),
  ('playwright._impl._browser_type',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_browser_type.py',
   'PYMODULE'),
  ('playwright._impl._cdp_session',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_cdp_session.py',
   'PYMODULE'),
  ('playwright._impl._clock',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_clock.py',
   'PYMODULE'),
  ('playwright._impl._connection',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_connection.py',
   'PYMODULE'),
  ('playwright._impl._console_message',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_console_message.py',
   'PYMODULE'),
  ('playwright._impl._dialog',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_dialog.py',
   'PYMODULE'),
  ('playwright._impl._download',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_download.py',
   'PYMODULE'),
  ('playwright._impl._driver',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_driver.py',
   'PYMODULE'),
  ('playwright._impl._element_handle',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_element_handle.py',
   'PYMODULE'),
  ('playwright._impl._errors',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_errors.py',
   'PYMODULE'),
  ('playwright._impl._event_context_manager',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_event_context_manager.py',
   'PYMODULE'),
  ('playwright._impl._fetch',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_fetch.py',
   'PYMODULE'),
  ('playwright._impl._file_chooser',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_file_chooser.py',
   'PYMODULE'),
  ('playwright._impl._frame',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_frame.py',
   'PYMODULE'),
  ('playwright._impl._glob',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_glob.py',
   'PYMODULE'),
  ('playwright._impl._greenlets',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_greenlets.py',
   'PYMODULE'),
  ('playwright._impl._har_router',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_har_router.py',
   'PYMODULE'),
  ('playwright._impl._helper',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_helper.py',
   'PYMODULE'),
  ('playwright._impl._impl_to_api_mapping',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_impl_to_api_mapping.py',
   'PYMODULE'),
  ('playwright._impl._input',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_input.py',
   'PYMODULE'),
  ('playwright._impl._js_handle',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_js_handle.py',
   'PYMODULE'),
  ('playwright._impl._json_pipe',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_json_pipe.py',
   'PYMODULE'),
  ('playwright._impl._local_utils',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_local_utils.py',
   'PYMODULE'),
  ('playwright._impl._locator',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_locator.py',
   'PYMODULE'),
  ('playwright._impl._map',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_map.py',
   'PYMODULE'),
  ('playwright._impl._network',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_network.py',
   'PYMODULE'),
  ('playwright._impl._object_factory',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_object_factory.py',
   'PYMODULE'),
  ('playwright._impl._page',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_page.py',
   'PYMODULE'),
  ('playwright._impl._playwright',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_playwright.py',
   'PYMODULE'),
  ('playwright._impl._selectors',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_selectors.py',
   'PYMODULE'),
  ('playwright._impl._set_input_files_helpers',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_set_input_files_helpers.py',
   'PYMODULE'),
  ('playwright._impl._str_utils',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_str_utils.py',
   'PYMODULE'),
  ('playwright._impl._stream',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_stream.py',
   'PYMODULE'),
  ('playwright._impl._sync_base',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py',
   'PYMODULE'),
  ('playwright._impl._tracing',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_tracing.py',
   'PYMODULE'),
  ('playwright._impl._transport',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_transport.py',
   'PYMODULE'),
  ('playwright._impl._video',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_video.py',
   'PYMODULE'),
  ('playwright._impl._waiter',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_waiter.py',
   'PYMODULE'),
  ('playwright._impl._web_error',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_web_error.py',
   'PYMODULE'),
  ('playwright._impl._writable_stream',
   'E:\\python\\Lib\\site-packages\\playwright\\_impl\\_writable_stream.py',
   'PYMODULE'),
  ('playwright._repo_version',
   'E:\\python\\Lib\\site-packages\\playwright\\_repo_version.py',
   'PYMODULE'),
  ('playwright.async_api',
   'E:\\python\\Lib\\site-packages\\playwright\\async_api\\__init__.py',
   'PYMODULE'),
  ('playwright.async_api._context_manager',
   'E:\\python\\Lib\\site-packages\\playwright\\async_api\\_context_manager.py',
   'PYMODULE'),
  ('playwright.async_api._generated',
   'E:\\python\\Lib\\site-packages\\playwright\\async_api\\_generated.py',
   'PYMODULE'),
  ('playwright.sync_api',
   'E:\\python\\Lib\\site-packages\\playwright\\sync_api\\__init__.py',
   'PYMODULE'),
  ('playwright.sync_api._context_manager',
   'E:\\python\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py',
   'PYMODULE'),
  ('playwright.sync_api._generated',
   'E:\\python\\Lib\\site-packages\\playwright\\sync_api\\_generated.py',
   'PYMODULE'),
  ('plistlib', 'E:\\python\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'E:\\python\\Lib\\pprint.py', 'PYMODULE'),
  ('profile', 'E:\\python\\Lib\\profile.py', 'PYMODULE'),
  ('prompt_toolkit',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\application\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\application\\application.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\application\\current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\application\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\application\\run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\cache.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\clipboard\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\clipboard\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\clipboard\\in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\completion\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\completion\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\completion\\deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\completion\\filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\completion\\nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\completion\\word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\document.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\enums.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\eventloop\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\eventloop\\async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\eventloop\\inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\eventloop\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\eventloop\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\filters\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\filters\\app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\filters\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\filters\\cli.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\filters\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\html.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\history.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\input\\win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\key_binding\\vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\keys.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\controls.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\margins.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\layout\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\lexers\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\lexers\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\lexers\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\output\\windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\selection.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\styles\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\styles\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\styles\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\styles\\named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\styles\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\styles\\style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\styles\\style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\validation.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\widgets\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\widgets\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\widgets\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\widgets\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\widgets\\toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   'E:\\python\\Lib\\site-packages\\prompt_toolkit\\win32_types.py',
   'PYMODULE'),
  ('propcache',
   'E:\\python\\Lib\\site-packages\\propcache\\__init__.py',
   'PYMODULE'),
  ('propcache._helpers',
   'E:\\python\\Lib\\site-packages\\propcache\\_helpers.py',
   'PYMODULE'),
  ('propcache._helpers_py',
   'E:\\python\\Lib\\site-packages\\propcache\\_helpers_py.py',
   'PYMODULE'),
  ('propcache.api',
   'E:\\python\\Lib\\site-packages\\propcache\\api.py',
   'PYMODULE'),
  ('pstats', 'E:\\python\\Lib\\pstats.py', 'PYMODULE'),
  ('pure_eval',
   'E:\\python\\Lib\\site-packages\\pure_eval\\__init__.py',
   'PYMODULE'),
  ('pure_eval.core',
   'E:\\python\\Lib\\site-packages\\pure_eval\\core.py',
   'PYMODULE'),
  ('pure_eval.my_getattr_static',
   'E:\\python\\Lib\\site-packages\\pure_eval\\my_getattr_static.py',
   'PYMODULE'),
  ('pure_eval.utils',
   'E:\\python\\Lib\\site-packages\\pure_eval\\utils.py',
   'PYMODULE'),
  ('pure_eval.version',
   'E:\\python\\Lib\\site-packages\\pure_eval\\version.py',
   'PYMODULE'),
  ('py_compile', 'E:\\python\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'E:\\python\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'E:\\python\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'E:\\python\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pyee', 'E:\\python\\Lib\\site-packages\\pyee\\__init__.py', 'PYMODULE'),
  ('pyee.asyncio',
   'E:\\python\\Lib\\site-packages\\pyee\\asyncio.py',
   'PYMODULE'),
  ('pyee.base', 'E:\\python\\Lib\\site-packages\\pyee\\base.py', 'PYMODULE'),
  ('pygments',
   'E:\\python\\Lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'E:\\python\\Lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'E:\\python\\Lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'E:\\python\\Lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'E:\\python\\Lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'E:\\python\\Lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'E:\\python\\Lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'E:\\python\\Lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'E:\\python\\Lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'E:\\python\\Lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'E:\\python\\Lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'E:\\python\\Lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'E:\\python\\Lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'E:\\python\\Lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'E:\\python\\Lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'E:\\python\\Lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'E:\\python\\Lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pymysql',
   'E:\\python\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   'E:\\python\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   'E:\\python\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   'E:\\python\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   'E:\\python\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'E:\\python\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'E:\\python\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'E:\\python\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'E:\\python\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'E:\\python\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'E:\\python\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   'E:\\python\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'E:\\python\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'E:\\python\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'E:\\python\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'E:\\python\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   'E:\\python\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pyreadline3',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'E:\\python\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pytz', 'E:\\python\\Lib\\site-packages\\pytz\\__init__.py', 'PYMODULE'),
  ('pytz.exceptions',
   'E:\\python\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy', 'E:\\python\\Lib\\site-packages\\pytz\\lazy.py', 'PYMODULE'),
  ('pytz.tzfile',
   'E:\\python\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'E:\\python\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'E:\\python\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'E:\\python\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'E:\\python\\Lib\\random.py', 'PYMODULE'),
  ('readline', 'E:\\python\\Lib\\site-packages\\readline.py', 'PYMODULE'),
  ('rlcompleter', 'E:\\python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'E:\\python\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'E:\\python\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'E:\\python\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'E:\\python\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\python\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\python\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\python\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\python\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\python\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\python\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\python\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\python\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\python\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\python\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'E:\\python\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\python\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'E:\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\python\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\python\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\python\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\python\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\python\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\python\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\python\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\python\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\python\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\python\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\python\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\python\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\python\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\python\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\python\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\python\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\python\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\python\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\python\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\python\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\python\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\python\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\python\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\python\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\python\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\python\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\python\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\python\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\python\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'E:\\python\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'E:\\python\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'E:\\python\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'E:\\python\\Lib\\site.py', 'PYMODULE'),
  ('six', 'E:\\python\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'E:\\python\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'E:\\python\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'E:\\python\\Lib\\socketserver.py', 'PYMODULE'),
  ('sqlalchemy',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'E:\\python\\Lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3', 'E:\\python\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'E:\\python\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'E:\\python\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'E:\\python\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'E:\\python\\Lib\\ssl.py', 'PYMODULE'),
  ('stack_data',
   'E:\\python\\Lib\\site-packages\\stack_data\\__init__.py',
   'PYMODULE'),
  ('stack_data.core',
   'E:\\python\\Lib\\site-packages\\stack_data\\core.py',
   'PYMODULE'),
  ('stack_data.formatting',
   'E:\\python\\Lib\\site-packages\\stack_data\\formatting.py',
   'PYMODULE'),
  ('stack_data.serializing',
   'E:\\python\\Lib\\site-packages\\stack_data\\serializing.py',
   'PYMODULE'),
  ('stack_data.utils',
   'E:\\python\\Lib\\site-packages\\stack_data\\utils.py',
   'PYMODULE'),
  ('stack_data.version',
   'E:\\python\\Lib\\site-packages\\stack_data\\version.py',
   'PYMODULE'),
  ('statistics', 'E:\\python\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'E:\\python\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'E:\\python\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'E:\\python\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'E:\\python\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'E:\\python\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'E:\\python\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'E:\\python\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'E:\\python\\Lib\\threading.py', 'PYMODULE'),
  ('timeit', 'E:\\python\\Lib\\timeit.py', 'PYMODULE'),
  ('token', 'E:\\python\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'E:\\python\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'E:\\python\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'E:\\python\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'E:\\python\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'E:\\python\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('traitlets',
   'E:\\python\\Lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'E:\\python\\Lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'E:\\python\\Lib\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'E:\\python\\Lib\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   'E:\\python\\Lib\\site-packages\\traitlets\\config\\argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'E:\\python\\Lib\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'E:\\python\\Lib\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'E:\\python\\Lib\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'E:\\python\\Lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'E:\\python\\Lib\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('tty', 'E:\\python\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'E:\\python\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'E:\\python\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'E:\\python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'E:\\python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'E:\\python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'E:\\python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'E:\\python\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'E:\\python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'E:\\python\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'E:\\python\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'E:\\python\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'E:\\python\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'E:\\python\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'E:\\python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'E:\\python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'E:\\python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'E:\\python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'E:\\python\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'E:\\python\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'E:\\python\\Lib\\uuid.py', 'PYMODULE'),
  ('wave', 'E:\\python\\Lib\\wave.py', 'PYMODULE'),
  ('wcwidth',
   'E:\\python\\Lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'E:\\python\\Lib\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'E:\\python\\Lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'E:\\python\\Lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'E:\\python\\Lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'E:\\python\\Lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser', 'E:\\python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'E:\\python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'E:\\python\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'E:\\python\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'E:\\python\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'E:\\python\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'E:\\python\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'E:\\python\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'E:\\python\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'E:\\python\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'E:\\python\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'E:\\python\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'E:\\python\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'E:\\python\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'E:\\python\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'E:\\python\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'E:\\python\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'E:\\python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'E:\\python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'E:\\python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'E:\\python\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'E:\\python\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'E:\\python\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'E:\\python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'E:\\python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc.server', 'E:\\python\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('yarl', 'E:\\python\\Lib\\site-packages\\yarl\\__init__.py', 'PYMODULE'),
  ('yarl._parse',
   'E:\\python\\Lib\\site-packages\\yarl\\_parse.py',
   'PYMODULE'),
  ('yarl._path', 'E:\\python\\Lib\\site-packages\\yarl\\_path.py', 'PYMODULE'),
  ('yarl._query',
   'E:\\python\\Lib\\site-packages\\yarl\\_query.py',
   'PYMODULE'),
  ('yarl._quoters',
   'E:\\python\\Lib\\site-packages\\yarl\\_quoters.py',
   'PYMODULE'),
  ('yarl._quoting',
   'E:\\python\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'E:\\python\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._url', 'E:\\python\\Lib\\site-packages\\yarl\\_url.py', 'PYMODULE'),
  ('zipfile', 'E:\\python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'E:\\python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'E:\\python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'E:\\python\\Lib\\zipimport.py', 'PYMODULE')])
