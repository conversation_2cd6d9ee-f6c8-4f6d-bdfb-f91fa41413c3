import asyncio
import random
import io
from typing import Optional, Dict, Any, List
import ddddocr
import aiohttp
import base64
from playwright.async_api import Page, ElementHandle

class ProxyManager:
    def __init__(self, api_url: str, max_usage_per_ip: int = 4, proxy_key: str = "YOUR_PROXY_KEY",
                 proxy_protocol: str = "http", username: Optional[str] = None, password: Optional[str] = None,
                 bypass: Optional[List[str]] = None):
        self.api_url_template = api_url
        self.proxy_key = proxy_key
        self.max_usage_per_ip = max_usage_per_ip
        self.proxy_protocol = proxy_protocol  # Support both 'http' and 'https'
        self.username = username  # Proxy authentication username
        self.password = password  # Proxy authentication password
        self.bypass = bypass or []  # Hosts to bypass proxy for
        self.current_proxy_server: Optional[str] = None  # Stores IP:PORT without schema
        self.current_proxy_usage: int = 0
        self._lock = asyncio.Lock()
        self.aio_session: Optional[aiohttp.ClientSession] = None

    async def _get_aio_session(self) -> aiohttp.ClientSession:
        if self.aio_session is None or self.aio_session.closed:
            self.aio_session = aiohttp.ClientSession()
        return self.aio_session

    async def close_aio_session(self):
        if self.aio_session and not self.aio_session.closed:
            await self.aio_session.close()
            self.aio_session = None
            print("代理获取会话已关闭。")

    async def _fetch_new_proxy(self) -> Optional[str]:
        request_url = f"https://share.proxy.qg.net/get?key={self.proxy_key}&num=1&area=&isp=0&format=txt&seq=%0D%0A&distinct=false"
        print(f"正在从 {request_url} 获取新的代理IP...")
        session = await self._get_aio_session()
        try:
            async with session.get(request_url, timeout=aiohttp.ClientTimeout(total=15)) as response:
                response.raise_for_status()
                proxy_str = await response.text()
                proxy_str = proxy_str.strip()
                if proxy_str and ":" in proxy_str and len(proxy_str.split(':')) == 2:
                    print(f"获取到新的代理IP: {proxy_str}")
                    return proxy_str
                else:
                    print(f"从API获取的代理格式无效: '{proxy_str}'")
                    return None
        except aiohttp.ClientConnectorError as e:
            print(f"获取代理IP连接错误: {e}")
            return None
        except asyncio.TimeoutError:
            print(f"获取代理IP超时。")
            return None
        except Exception as e:
            print(f"获取代理IP时发生未知错误: {e}")
            return None

    async def get_proxy_config(self) -> Optional[Dict[str, Any]]:
        async with self._lock:
            if self.current_proxy_server and self.current_proxy_usage < self.max_usage_per_ip:
                # self.current_proxy_server is IP:PORT, Playwright needs schema
                return self._build_proxy_config(self.current_proxy_server)

            new_proxy_server = await self._fetch_new_proxy()  # Returns IP:PORT
            if new_proxy_server:
                self.current_proxy_server = new_proxy_server
                self.current_proxy_usage = 0
                print(f"切换到新代理: {self.current_proxy_server} (协议: {self.proxy_protocol})")
                return self._build_proxy_config(self.current_proxy_server)
            else:
                self.current_proxy_server = None
                self.current_proxy_usage = 0
                print("无法获取新的有效代理IP。")
                return None

    def _build_proxy_config(self, proxy_server: str) -> Dict[str, Any]:
        """Build complete proxy configuration for Playwright"""
        config = {"server": f"{self.proxy_protocol}://{proxy_server}"}

        # Add authentication if provided
        if self.username:
            config["username"] = self.username
        if self.password:
            config["password"] = self.password

        # Add bypass list if provided
        if self.bypass:
            config["bypass"] = ",".join(self.bypass)

        return config

    async def increment_proxy_usage(self, proxy_server_used_with_schema: str):
        async with self._lock:
            # proxy_server_used_with_schema comes from Playwright context (e.g., "http://IP:PORT")
            # self.current_proxy_server is stored as "IP:PORT"
            normalized_proxy_used = proxy_server_used_with_schema.replace("http://", "").replace("https://", "")
            if self.current_proxy_server == normalized_proxy_used:
                self.current_proxy_usage += 1
                if self.current_proxy_usage >= self.max_usage_per_ip:
                    print(f"代理 {self.current_proxy_server} 已达到最大使用次数 ({self.max_usage_per_ip})，下次将获取新代理。")

class HonorWarrantyQuerier:
    def __init__(self):
        self.ocr = ddddocr.DdddOcr(det=False, ocr=True, show_ad=False)
        self.aio_session: Optional[aiohttp.ClientSession] = None

    async def _get_aio_session(self) -> aiohttp.ClientSession:
        if self.aio_session is None or self.aio_session.closed:
            self.aio_session = aiohttp.ClientSession()
        return self.aio_session

    async def close_aio_session(self):
        if self.aio_session and not self.aio_session.closed:
            await self.aio_session.close()
            self.aio_session = None

    async def _get_image_bytes_from_element(self, image_element: ElementHandle) -> Optional[bytes]:
        try:
            img_src = await image_element.get_attribute('src')
            if not img_src: return None
            if img_src.startswith('data:image'):
                _, encoded = img_src.split(',', 1)
                return io.BytesIO(base64.b64decode(encoded)).read()
            else:
                session = await self._get_aio_session()
                async with session.get(img_src, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    response.raise_for_status()
                    return await response.read()
        except Exception as e:
            print(f"获取验证码图片时出错: {e}")
            return None

    async def solve_image_captcha(self, page: Page, captcha_img_selector: str) -> Optional[str]:
        try:
            captcha_image_element = await page.query_selector(captcha_img_selector)
            if not captcha_image_element:
                print(f"未找到验证码图片元素。")
                return None
            try:
                await captcha_image_element.wait_for_element_state("visible", timeout=3000)
            except Exception:
                print("验证码图片在超时前未变得可见。")
                return None
            await asyncio.sleep(random.uniform(0.1, 0.2))
            image_bytes = await self._get_image_bytes_from_element(captcha_image_element)
            if not image_bytes: return None
            captcha_text = self.ocr.classification(image_bytes)
            if captcha_text and len(captcha_text) > 0:
                cleaned_text = ''.join(filter(str.isalnum, captcha_text)).lower()
                if len(cleaned_text) == 4 : return cleaned_text
                else: print(f"识别的验证码 '{cleaned_text}' (原始: {captcha_text}) 格式无效。")
            else: print(f"未能识别验证码 (原始输出: {captcha_text})。")
            return None
        except Exception as e:
            print(f"识别验证码时出错: {e}")
            return None

    async def fill_form_and_submit(self, page: Page, serial_number: str, captcha_text: str) -> bool:
        try:
            sn_input = await page.query_selector('input[placeholder="输入设备序列号"]')
            if not sn_input: print("未找到序列号输入框。"); return False
            await sn_input.fill("")
            await sn_input.type(serial_number, delay=random.uniform(15, 50))
            await asyncio.sleep(random.uniform(0.05, 0.15))

            captcha_input = await page.query_selector('input[placeholder="验证码"]')
            if not captcha_input: print("未找到验证码输入框。"); return False
            await captcha_input.fill("")
            await captcha_input.type(captcha_text, delay=random.uniform(15, 50))
            await asyncio.sleep(random.uniform(0.05, 0.15))

            submit_button = await page.query_selector('div.warranty-inquiry-btn > a[title="查询"]')
            if not submit_button: print("未找到提交按钮。"); return False
            if 's-disabled' in (await submit_button.get_attribute('class') or ''):
                try: await submit_button.wait_for_element_state("enabled", timeout=2500)
                except Exception: print("提交按钮仍然禁用。"); return False
            await submit_button.click(timeout=3000)
            try: await page.wait_for_load_state('domcontentloaded', timeout=7000)
            except Exception: pass
            await asyncio.sleep(random.uniform(0.4, 0.8))
            return True
        except Exception as e:
            print(f"提交查询时出错: {e}")
            return False

    async def check_for_errors_after_submit(self, page: Page) -> Optional[str]:
        error_selectors = {
            ".wic-device .wrong", ".wic-identify-wrap > span[role='alert']", "div.form-error-banner"
        }
        for selector in error_selectors:
            error_element = await page.query_selector(f"{selector} >> visible=true")
            if error_element:
                error_text = (await error_element.text_content() or "").strip()
                if error_text: return error_text
        return None

    async def refresh_captcha_if_needed(self, page: Page, captcha_img_selector: str) -> bool:
        try:
            captcha_image_element = await page.query_selector(captcha_img_selector)
            if captcha_image_element:
                old_src = await captcha_image_element.get_attribute('src')
                await captcha_image_element.click(timeout=2000)
                for _ in range(20):
                    await asyncio.sleep(0.15)
                    new_src = await captcha_image_element.get_attribute('src')
                    if new_src != old_src and new_src:
                        await asyncio.sleep(0.15)
                        return True
                print("已点击验证码图片，但图片未在预期内刷新。")
                return False
            else: print("未找到用于刷新的验证码图片。"); return False
        except Exception as e:
            print(f"刷新验证码时出错: {e}")
            return False

    async def query_warranty(self, page: Page, serial_number: str, max_attempts: int = 3) -> str:
        captcha_img_selector = '.wic-identify .wiw-img img'
        success_indicator_selector = "div.warranty-content-guarantee div.g-i-n.clearfix"
        for attempt in range(1, max_attempts + 1):
            if await page.query_selector(f"{success_indicator_selector} >> visible=true"): return "成功"
            captcha_text = await self.solve_image_captcha(page, captcha_img_selector)
            if not captcha_text:
                print(f"SN: {serial_number} (尝试 {attempt}) - 验证码识别失败。")
                if attempt < max_attempts: await self.refresh_captcha_if_needed(page, captcha_img_selector); continue
                else: return "验证码识别失败_达到最大尝试次数"
            submitted = await self.fill_form_and_submit(page, serial_number, captcha_text)
            if not submitted:
                print(f"SN: {serial_number} (尝试 {attempt}) - 表单提交流程失败。")
                if attempt < max_attempts: await self.refresh_captcha_if_needed(page, captcha_img_selector); continue
                return "表单提交失败"
            await asyncio.sleep(0.6)
            if await page.query_selector(f"{success_indicator_selector} >> visible=true"): return "成功"
            error_message = await self.check_for_errors_after_submit(page)
            if error_message:
                print(f"SN: {serial_number} (尝试 {attempt}) - 提交导致错误: {error_message}")
                if ("验证码" in error_message or "captcha" in error_message.lower()) and attempt < max_attempts:
                    await self.refresh_captcha_if_needed(page, captcha_img_selector); continue
                elif "序列号" in error_message: return "设备序列号无效"
                if attempt < max_attempts: await self.refresh_captcha_if_needed(page, captcha_img_selector); continue
                return f"提交后出错: {error_message[:100]}"
            no_results_element = await page.query_selector(".result-none-box .none-title >> visible=true")
            if no_results_element:
                return "未找到结果"
            print(f"SN: {serial_number} (尝试 {attempt}) - 结果不明确。")
            if attempt < max_attempts: await self.refresh_captcha_if_needed(page, captcha_img_selector); continue
            return "失败_结果不明确_达到最大尝试次数"
        return "失败_达到最大尝试次数"

    async def _get_text_safe(self, parent: Optional[ElementHandle], sel: str, default: str = "未提供") -> str:
        if not parent: return default
        try:
            el = await parent.query_selector(sel + " >> visible=true")
            if el: text = await el.text_content(); return text.strip() if text else default
        except: pass
        return default

    async def extract_results(self, page: Page, sn_queried: str) -> Optional[Dict[str, Any]]:
        container = await page.query_selector("div.warranty-content-guarantee div.g-i-n.clearfix >> visible=true")
        if not container: print(f"SN: {sn_queried} - 产品信息容器未找到。"); return None
        data = {"产品信息": {}, "查询序列号": sn_queried}
        data["产品信息"]["产品名称"] = await self._get_text_safe(container, "div.g-i-right > span.p1")
        sn_div = await container.query_selector("div.g-i-right div.effective > div:has(span.in-span-label:text-matches('序列号：', 'i'))")
        data["产品信息"]["显示序列号"] = await self._get_text_safe(sn_div, "span.in-span-value")
        reg_div = await container.query_selector("div.g-i-right div.effective > div:has(span.in-span-label:text-matches('可使用范围：', 'i'))")
        data["产品信息"]["适用范围"] = await self._get_text_safe(reg_div, "span.in-span-value")
        img_el = await container.query_selector("div.g-i-left img.img")
        data["产品信息"]["图片链接"] = await img_el.get_attribute("src") or "未提供" if img_el else "未提供"
        return data
