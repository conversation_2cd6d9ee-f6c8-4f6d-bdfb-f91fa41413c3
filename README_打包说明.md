# 华为/荣耀设备保修查询工具 - 打包说明

## 项目概述

这是一个用于查询华为和荣耀设备保修信息的桌面应用程序，支持批量查询、代理设置、并发控制等功能。

## 打包要求

### 系统要求
- Windows 10/11 (64位)
- Python 3.8 或更高版本
- 至少 2GB 可用磁盘空间
- 网络连接（用于下载依赖）

### 必要文件
确保以下文件存在于项目目录中：
- `honor_warranty_app.py` - 主程序文件
- `honor_warranty_querier.py` - 荣耀查询模块
- `huawei_new2_update_3.py` - 华为查询模块
- `disclaimer_txt.py` - 免责声明文本
- `honor_logo.ico` - 应用程序图标
- `honor_logo.png` - 应用程序图标(PNG格式)
- `warning_icon.png` - 警告图标
- `VC_redist.x64.exe` - VC++ 运行库安装包

## 打包方法

### 方法一：使用自动打包脚本（推荐）

1. **运行批处理文件**
   ```
   双击 build.bat
   ```

2. **或者直接运行Python脚本**
   ```bash
   python build_exe.py
   ```

### 方法二：手动打包

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **清理旧的构建文件**
   ```bash
   rmdir /s build dist
   ```

3. **执行打包**
   ```bash
   pyinstaller --clean honor_warranty_app.spec
   ```

## 打包配置说明

### PyInstaller配置 (honor_warranty_app.spec)

- **单文件模式**: 生成单个exe文件，便于分发
- **无控制台**: 隐藏命令行窗口，提供更好的用户体验
- **图标设置**: 使用自定义图标 `honor_logo.ico`
- **资源文件**: 自动包含所有必要的资源文件
- **排除Playwright**: 运行时动态安装，减小文件大小

### 特殊处理

1. **Playwright动态安装**
   - 首次运行时自动检测并安装Playwright
   - 自动下载Chromium浏览器
   - 显示安装进度对话框

2. **VC++ Redistributable**
   - 自动检测并安装VC++运行库
   - 静默安装，无需用户干预

## 生成的文件

打包完成后，在 `dist` 目录下会生成：
- `华为荣耀设备保修查询工具.exe` - 主程序文件

## 分发说明

### 文件大小
- 预期大小：约 50-80 MB
- 首次运行后会下载约 100-200 MB 的浏览器组件

### 系统要求（目标机器）
- Windows 10/11 (64位)
- 网络连接（首次运行时需要）
- 管理员权限（安装VC++运行库时可能需要）

### 首次运行
1. 双击运行 `华为荣耀设备保修查询工具.exe`
2. 程序会自动检测并安装必要组件
3. 显示安装进度对话框
4. 安装完成后正常启动程序

## 故障排除

### 常见问题

1. **打包失败**
   - 检查Python版本是否为3.8+
   - 确保所有必要文件存在
   - 检查网络连接

2. **运行时错误**
   - 确保目标机器有网络连接
   - 检查防火墙设置
   - 尝试以管理员身份运行

3. **浏览器组件安装失败**
   - 检查网络连接
   - 尝试手动安装：`pip install playwright && playwright install chromium`
   - 检查磁盘空间

### 日志和调试

程序运行时会在控制台输出详细日志，包括：
- 初始化过程
- 查询进度
- 错误信息
- 网络请求状态

## 技术细节

### 依赖库
- **PyQt6**: GUI界面框架
- **Playwright**: 浏览器自动化（运行时安装）
- **pandas**: Excel文件处理
- **ddddocr**: 验证码识别
- **aiohttp**: 异步HTTP请求

### 架构特点
- 异步并发查询
- 智能代理管理
- 自动验证码识别
- 动态UI状态更新
- 内存优化的线程管理

## 版本信息

- 版本：1.0
- 构建工具：PyInstaller 5.0+
- 目标平台：Windows x64
- Python版本：3.8+

## 联系信息

如有问题或建议，请联系开发团队。
