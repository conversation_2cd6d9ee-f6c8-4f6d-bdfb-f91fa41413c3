# --- START OF FILE huawei_new2_update_2.py ---
import asyncio
import random
import time
from playwright.async_api import (
    async_playwright,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    BrowserContext,
)
from urllib.parse import urljoin
from typing import Optional, <PERSON><PERSON>, Dict, Any, List
import ddddocr
import aiohttp
import io
import traceback
import json
import pandas as pd


# --- ProxyManager Class ---
class ProxyManager:
    def __init__(
        self, api_url: str, max_usage_per_ip: int = 4, proxy_key: str = "YOUR_PROXY_KEY",
        proxy_protocol: str = "http", username: Optional[str] = None, password: Optional[str] = None,
        bypass: Optional[List[str]] = None
    ):
        self.api_url_template = api_url
        self.proxy_key = proxy_key
        self.max_usage_per_ip = max_usage_per_ip
        self.proxy_protocol = proxy_protocol  # Support both 'http' and 'https'
        self.username = username  # Proxy authentication username
        self.password = password  # Proxy authentication password
        self.bypass = bypass or []  # Hosts to bypass proxy for
        self.current_proxy_server: Optional[str] = None  # Stores IP:PORT without schema
        self.current_proxy_usage: int = 0
        self._lock = asyncio.Lock()
        self.aio_session_proxy: Optional[aiohttp.ClientSession] = None

    async def _get_aio_session_proxy(self) -> aiohttp.ClientSession:
        if self.aio_session_proxy is None or self.aio_session_proxy.closed:
            self.aio_session_proxy = aiohttp.ClientSession()
        return self.aio_session_proxy

    async def close_aio_session_proxy(self):
        if self.aio_session_proxy and not self.aio_session_proxy.closed:
            await self.aio_session_proxy.close()
            self.aio_session_proxy = None

    async def _fetch_new_proxy(self) -> Optional[str]:
        request_url = f"https://share.proxy.qg.net/get?key={self.proxy_key}&num=1&area=&isp=0&format=txt&seq=%0D%0A&distinct=false"
        session = await self._get_aio_session_proxy()
        try:
            # 优化：减少超时时间，提高响应速度
            timeout = 8 if PERFORMANCE_CONFIG.get("optimized_timeouts") else 15
            async with session.get(
                request_url, timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                response.raise_for_status()
                proxy_str = await response.text()
                proxy_str = proxy_str.strip()
                if proxy_str and ":" in proxy_str and len(proxy_str.split(":")) == 2:
                    return proxy_str
                else:
                    print(f"[错误] 从API获取的代理格式无效: '{proxy_str}'")
                    return None
        except aiohttp.ClientConnectorError as e:
            print(f"[错误] 获取代理IP连接错误: {e}")
            return None
        except asyncio.TimeoutError:
            print(f"[错误] 获取代理IP超时")
            return None
        except Exception as e:
            print(f"[错误] 获取代理IP时发生未知错误: {e}")
            return None

    async def get_proxy_config(self) -> Optional[Dict[str, Any]]:
        async with self._lock:
            if (
                self.current_proxy_server
                and self.current_proxy_usage < self.max_usage_per_ip
            ):
                # self.current_proxy_server is IP:PORT, Playwright needs schema
                return self._build_proxy_config(self.current_proxy_server)

            new_proxy_server = await self._fetch_new_proxy()  # Returns IP:PORT
            if new_proxy_server:
                self.current_proxy_server = new_proxy_server
                self.current_proxy_usage = 0
                print(f"[信息] 切换到新代理: {self.current_proxy_server} (协议: {self.proxy_protocol})")
                return self._build_proxy_config(self.current_proxy_server)
            else:
                self.current_proxy_server = None  # Reset if fetch fails
                self.current_proxy_usage = 0
                print("[警告] 无法获取新的有效代理IP")
                return None

    def _build_proxy_config(self, proxy_server: str) -> Dict[str, Any]:
        """Build complete proxy configuration for Playwright"""
        config = {"server": f"{self.proxy_protocol}://{proxy_server}"}

        # Add authentication if provided
        if self.username:
            config["username"] = self.username
        if self.password:
            config["password"] = self.password

        # Add bypass list if provided
        if self.bypass:
            config["bypass"] = ",".join(self.bypass)

        return config

    async def increment_proxy_usage(self, proxy_server_used_with_schema: str):
        async with self._lock:
            # proxy_server_used_with_schema comes from Playwright context (e.g., "http://IP:PORT")
            # self.current_proxy_server is stored as "IP:PORT"
            normalized_proxy_used = proxy_server_used_with_schema.replace(
                "http://", ""
            ).replace("https://", "")
            if self.current_proxy_server == normalized_proxy_used:
                self.current_proxy_usage += 1
                if self.current_proxy_usage >= self.max_usage_per_ip:
                    print(
                        f"[信息] 代理 {self.current_proxy_server} 已达到最大使用次数 ({self.max_usage_per_ip})"
                    )


# --- SliderCaptchaHandler Class ---
class SliderCaptchaHandler:
    def __init__(self):
        self.ocr = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
        self.common_slider_selectors = [
            ".slider-button",
            ".slide-button",
            ".slider-handle",
            ".captcha-slider",
            ".verify-slider",
            '[class*="slider"]',
            '[class*="slide"]',
            ".nc_iconfont",
            ".secsdk-captcha-drag-icon",
            ".yidun_slider",
            ".tc-slider",
            ".slide-verify-slider-mask-item",
            ".geetest_slider",
            ".verify-move-block",
        ]
        self.slider_container_selectors = [
            ".slider-container",
            ".captcha-container",
            ".verify-container",
            '[class*="captcha"]',
            '[class*="verify"]',
            ".nc-container",
            ".secsdk-captcha-wrapper",
            ".yidun",
            ".yidun_popup",
            ".tc-wrap",
            ".geetest_widget",
            ".slide-verify",
        ]
        self.yidun_bg_image_selector = ".yidun_bg-img"
        self.yidun_puzzle_image_selector = ".yidun_jigsaw"
        self.yidun_slider_piece_selector = ".yidun_slider"  # The draggable piece
        self.yidun_refresh_selector = ".yidun_refresh"
        self.target_success_element_selector = (
            "div.warranty-content-guarantee"  # Element indicating query success
        )
        self.aio_session_img: Optional[aiohttp.ClientSession] = None

    async def _get_aio_session_img(self) -> aiohttp.ClientSession:
        if self.aio_session_img is None or self.aio_session_img.closed:
            self.aio_session_img = aiohttp.ClientSession()
        return self.aio_session_img

    async def close_aio_session_img(self):
        if self.aio_session_img and not self.aio_session_img.closed:
            await self.aio_session_img.close()
            self.aio_session_img = None

    async def _get_image_bytes(self, url: str) -> Optional[bytes]:
        session = await self._get_aio_session_img()
        try:
            # 优化：减少图片下载超时时间
            timeout = 5 if PERFORMANCE_CONFIG.get("optimized_timeouts") else 10
            async with session.get(
                url, timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                response.raise_for_status()
                return await response.read()
        except Exception as e:
            print(f"[错误] 获取图片失败 {url}: {e}")
            return None

    async def get_slide_distance_ddddocr(self, page: Page) -> Optional[float]:
        try:
            # 优化：减少等待时间，提高响应速度
            timeout = 2000 if PERFORMANCE_CONFIG.get("optimized_timeouts") else 3000
            await page.wait_for_selector(
                self.yidun_bg_image_selector, state="visible", timeout=timeout
            )
            await page.wait_for_selector(
                self.yidun_puzzle_image_selector, state="visible", timeout=timeout
            )
            # 优化：减少随机延迟
            if PERFORMANCE_CONFIG.get("reduced_delays"):
                await asyncio.sleep(random.uniform(0.05, 0.15))
            else:
                await asyncio.sleep(random.uniform(0.15, 0.3))

            bg_image_element = await page.query_selector(self.yidun_bg_image_selector)
            puzzle_image_element = await page.query_selector(
                self.yidun_puzzle_image_selector
            )

            if not bg_image_element or not puzzle_image_element:
                print("[错误] 背景或滑块图片元素未找到")
                return None

            bg_image_url = await bg_image_element.get_attribute("src")
            puzzle_image_url = await puzzle_image_element.get_attribute("src")

            if not bg_image_url or not puzzle_image_url:
                print("[错误] 背景或滑块图片URL未找到")
                return None

            # Ensure URLs are absolute
            if bg_image_url and not bg_image_url.startswith(('http:', 'https:', 'data:')):
                bg_image_url = urljoin(page.url, bg_image_url)
            if puzzle_image_url and not puzzle_image_url.startswith(('http:', 'https:', 'data:')):
                puzzle_image_url = urljoin(page.url, puzzle_image_url)

            bg_image_bytes = await self._get_image_bytes(bg_image_url)
            puzzle_image_bytes = await self._get_image_bytes(puzzle_image_url)

            if not bg_image_bytes or not puzzle_image_bytes:
                print("[错误] 下载图片字节失败")
                return None

            res = self.ocr.slide_match(
                puzzle_image_bytes, bg_image_bytes, simple_target=True
            )

            if res and "target" in res:
                raw_target_x = res["target"][0]
                adjusted_target_x = float(raw_target_x)

                # --- Your adjustment logic ---
                OFFSET_FOR_LARGE_DISTANCE = 10
                SCALE_FACTOR_FOR_LARGE_DISTANCE = 0.95
                THRESHOLD_X = 100

                if adjusted_target_x > THRESHOLD_X:
                    # print(f"原始 target_x: {adjusted_target_x:.2f} (大于 {THRESHOLD_X})，进行调整。")
                    adjusted_target_x = (
                        THRESHOLD_X
                        + (adjusted_target_x - THRESHOLD_X)
                        * SCALE_FACTOR_FOR_LARGE_DISTANCE
                    )
                    adjusted_target_x -= OFFSET_FOR_LARGE_DISTANCE
                    adjusted_target_x = max(THRESHOLD_X * 0.8, adjusted_target_x)
                    # print(f"调整后 target_x: {adjusted_target_x:.2f}")
                # --- Adjustment logic end ---

                final_distance = adjusted_target_x - 7  # Original common offset

                # print(f"计算原始 target_x: {raw_target_x}, 调整后: {adjusted_target_x:.2f}, 最终用于滑动的距离: {final_distance:.2f}")
                return (
                    final_distance if final_distance > 5 else adjusted_target_x
                )  # Ensure a minimum meaningful distance

            print("[错误] 未能确定滑动目标(res无target)")
            return None
        except Exception as e:
            print(f"[错误] 计算滑动距离时出错: {e}")
            # traceback.print_exc() # Uncomment for debugging
            return None

    async def fill_form_and_submit(self, page: Page, serial_number: str) -> bool:
        try:
            # 优化：减少页面加载等待时间
            timeout = 6000 if PERFORMANCE_CONFIG.get("optimized_timeouts") else 10000
            await page.wait_for_load_state("domcontentloaded", timeout=timeout)
            # 优化：减少随机延迟
            if PERFORMANCE_CONFIG.get("reduced_delays"):
                await asyncio.sleep(random.uniform(0.1, 0.3))
            else:
                await asyncio.sleep(random.uniform(0.3, 0.6))

            serial_input_selectors = [
                ".wic-device-wrap .input-wrap input",
                'input[placeholder="输入设备序列号"]',
                'input[placeholder*="序列号"]',  # More generic placeholder
                'input[maxlength="30"]',  # Common attribute
                'input[name*="sn"]',
                'input[id*="sn"]',  # Common name/id
            ]
            serial_input: Optional[ElementHandle] = None
            for selector in serial_input_selectors:
                serial_input = await page.query_selector(selector)
                if serial_input and await serial_input.is_visible():
                    break

            if serial_input:
                await serial_input.scroll_into_view_if_needed()
                # 优化：减少操作间延迟
                if PERFORMANCE_CONFIG.get("reduced_delays"):
                    await asyncio.sleep(random.uniform(0.02, 0.05))
                    await serial_input.click(timeout=2000, delay=random.uniform(15, 40))
                    await asyncio.sleep(random.uniform(0.02, 0.05))
                    await serial_input.fill("")  # Clear field
                    await asyncio.sleep(random.uniform(0.01, 0.03))
                    await serial_input.type(serial_number, delay=random.uniform(10, 35))
                    await asyncio.sleep(random.uniform(0.05, 0.15))
                else:
                    await asyncio.sleep(random.uniform(0.05, 0.1))
                    await serial_input.click(timeout=3000, delay=random.uniform(30, 80))
                    await asyncio.sleep(random.uniform(0.05, 0.1))
                    await serial_input.fill("")  # Clear field
                    await asyncio.sleep(random.uniform(0.03, 0.08))
                    await serial_input.type(serial_number, delay=random.uniform(20, 70))
                    await asyncio.sleep(random.uniform(0.15, 0.3))
                await serial_input.dispatch_event("change")  # Crucial for some frameworks
                await serial_input.dispatch_event("blur")  # Simulate losing focus
            else:
                print(f"[错误] SN: {serial_number} - 未找到序列号输入框")
                return False

            query_button_selectors = [
                '.warranty-inquiry-btn a[title="查询"]',
                ".warranty-inquiry-btn a.s-btn",
                'button:has-text("查询")',
                'a:has-text("查询")',  # Text based
                '[type="submit"]',
                '[role="button"][class*="query"]',
            ]
            query_button: Optional[ElementHandle] = None
            for selector in query_button_selectors:
                query_button = await page.query_selector(selector)
                if query_button and await query_button.is_visible():
                    break

            if query_button:
                await query_button.scroll_into_view_if_needed()
                # 优化：减少等待延迟
                if PERFORMANCE_CONFIG.get("reduced_delays"):
                    await asyncio.sleep(random.uniform(0.02, 0.05))
                else:
                    await asyncio.sleep(random.uniform(0.05, 0.1))

                # Wait for button to be enabled - 优化超时时间
                try:
                    timeout = 3000 if PERFORMANCE_CONFIG.get("optimized_timeouts") else 5000
                    await query_button.wait_for_element_state("enabled", timeout=timeout)
                except Exception:
                    # Check if it's already considered enabled or if disabled attribute is not present
                    is_disabled = await query_button.is_disabled()
                    has_disabled_class = "s-disabled" in (
                        await query_button.get_attribute("class") or ""
                    )
                    if is_disabled or has_disabled_class:
                        print(
                            f"[错误] SN: {serial_number} - 查询按钮长时间未激活或仍为禁用状态"
                        )
                        return False

                # 优化：减少点击延迟
                if PERFORMANCE_CONFIG.get("reduced_delays"):
                    await query_button.click(timeout=2000, delay=random.uniform(25, 50))
                    await asyncio.sleep(random.uniform(0.4, 0.9))  # Wait for AJAX or slider
                else:
                    await query_button.click(timeout=3000, delay=random.uniform(50, 100))
                    await asyncio.sleep(random.uniform(0.8, 1.8))  # Wait for AJAX or slider
                return True
            else:
                print(f"[错误] SN: {serial_number} - 未找到查询按钮")
                return False
        except Exception as e:
            print(f"[错误] SN: {serial_number} - 填写表单或提交时出错: {e}")
            # traceback.print_exc()
            return False

    async def wait_for_slider_appearance(
        self, page: Page, timeout: int = 5
    ) -> bool:
        # 优化：根据性能配置调整超时和检查间隔
        if PERFORMANCE_CONFIG.get("optimized_timeouts"):
            timeout = min(timeout, 3)  # 最大3秒超时
            check_interval = 0.1  # 更频繁的检查
        else:
            check_interval = 0.2

        start_time = time.time()
        while time.time() - start_time < timeout:
            if await self.detect_slider_captcha(page):
                return True
            await asyncio.sleep(check_interval)
        return False

    async def detect_slider_captcha(self, page: Page) -> bool:
        # Check for Yidun specifically, as it's common for this site
        yidun_selectors = [".yidun >> visible=true", ".yidun_popup >> visible=true"]
        for sel in yidun_selectors:
            if await page.query_selector(sel):
                return True
            for frame in page.frames:
                if not frame.is_detached() and await frame.query_selector(sel):
                    return True

        # General slider selectors
        for slider_selector in self.common_slider_selectors:
            sel_visible = slider_selector + " >> visible=true"
            if await page.query_selector(sel_visible):
                return True
            for frame in page.frames:
                if not frame.is_detached() and await frame.query_selector(sel_visible):
                    return True
        return False

    async def find_slider_element(self, page: Page) -> Optional[ElementHandle]:
        # Prioritize Yidun slider element if its container is visible
        yidun_container_visible_selectors = [
            ".yidun >> visible=true",
            ".yidun_popup >> visible=true",
        ]
        for yidun_container_sel in yidun_container_visible_selectors:
            if await page.query_selector(yidun_container_sel):
                # Now look for the specific draggable piece
                slider_el = await page.query_selector(
                    self.yidun_slider_piece_selector + " >> visible=true"
                )
                if slider_el:
                    return slider_el
                # Check in frames for Yidun slider piece
                for frame in page.frames:
                    if not frame.is_detached():
                        slider_el_frame = await frame.query_selector(
                            self.yidun_slider_piece_selector + " >> visible=true"
                        )
                        if slider_el_frame:
                            return slider_el_frame
                break  # Found yidun container, no need to check other yidun container selectors

        # General selectors if Yidun specific not found or not active
        for selector in self.common_slider_selectors:
            sel_visible = selector + " >> visible=true"
            element = await page.query_selector(sel_visible)
            if element:
                return element
            for frame in page.frames:
                if not frame.is_detached():
                    element_in_frame = await frame.query_selector(sel_visible)
                    if element_in_frame:
                        return element_in_frame
        print("[错误] 未能找到滑块拖动元素")
        return None

    async def get_slider_track_width(
        self, page: Page, slider_element: ElementHandle
    ) -> float:
        # Attempt ddddocr first, as it's more accurate for target position
        ddddocr_distance = await self.get_slide_distance_ddddocr(page)
        if (
            ddddocr_distance is not None and ddddocr_distance > 10
        ):  # Use ddddocr if successful and distance is reasonable
            print(f"[信息] 确定滑动距离: {ddddocr_distance:.2f}")
            return ddddocr_distance

        print("[信息] 计算距离失败/过小，尝试备用方法获取大致轨迹宽度")
        # Fallback: Try to get width from background image or track element
        track_element = await page.query_selector(
            self.yidun_bg_image_selector + " >> visible=true"
        )  # Yidun background
        if not track_element:
            # General track selectors if Yidun specific not found
            generic_track_selectors = [
                ".slider-track",
                ".slide-track",
                ".yidun_bg",
                ".geetest_bg",
            ]
            for sel in generic_track_selectors:
                track_element = await page.query_selector(sel + " >> visible=true")
                if track_element:
                    break

        if track_element:
            track_box = await track_element.bounding_box()
            if track_box and track_box["width"] > 20:
                # Estimate distance. Usually, the background width is the track width.
                # Slider piece width needs to be considered for a more precise end point, but ddddocr handles this.
                # For this fallback, a slight reduction for safety.
                distance = (
                    track_box["width"] * 0.95
                )  # Adjust factor as needed, or subtract a fixed offset
                print(
                    f"[信息] 备用方法(元素宽度)计算滑动距离: {distance:.2f} (来自元素宽度: {track_box['width']})"
                )
                return float(distance)

        print("[警告] 无法通过备用方法确定滑动距离，使用默认值 260")
        return 260.0  # Default fallback if all else fails

    def generate_human_like_path(self, distance: float) -> list:
        path, current_x = [], 0
        total_duration = random.uniform(0.6, 1.3)  # Total time for the slide
        num_steps = random.randint(30, 50)  # Number of mouse movements

        def ease_in_out_quad(t: float) -> float:  # Slightly different easing
            return 2 * t * t if t < 0.5 else 1 - pow(-2 * t + 2, 2) / 2

        for i in range(num_steps):
            prog = (i + 1) / num_steps
            current_x_target = distance * ease_in_out_quad(prog)

            if prog > 0.85 and random.random() < 0.20:  # More chance to adjust near end
                current_x_target += random.uniform(-distance * 0.04, distance * 0.025)

            current_x = max(
                0, min(distance + 5, current_x_target)
            )  # Clamp with slight overshoot allowance

            path.append(
                {
                    "x": current_x,
                    "y": random.uniform(-2.0, 2.0),  # Slightly more y-axis deviation
                    "delay": (total_duration / num_steps) * random.uniform(0.75, 1.25),
                }
            )

        if not path or abs(path[-1]["x"] - distance) > 4:
            path.append(
                {
                    "x": distance,
                    "y": random.uniform(-1, 1),
                    "delay": random.uniform(0.04, 0.09),
                }
            )
        else:
            path[-1]["x"] = distance
            path[-1]["y"] = random.uniform(-1, 1)

        if random.random() < 0.20:  # Higher chance for micro-adjustment
            path.append(
                {
                    "x": distance + random.uniform(-2.5, 1.5),
                    "y": random.uniform(-1, 1),
                    "delay": random.uniform(0.05, 0.12),
                }
            )
            path.append(
                {
                    "x": distance,
                    "y": random.uniform(-1, 1),
                    "delay": random.uniform(0.03, 0.06),
                }
            )
        return path

    async def perform_slider_captcha(self, page: Page) -> bool:
        try:
            slider = await self.find_slider_element(page)
            if not slider:
                print("[错误] 执行滑块操作前未找到滑块元素")
                return False

            await slider.scroll_into_view_if_needed()
            # 优化：减少滑块操作前的等待时间
            if PERFORMANCE_CONFIG.get("reduced_delays"):
                await asyncio.sleep(random.uniform(0.05, 0.1))
            else:
                await asyncio.sleep(random.uniform(0.1, 0.2))

            slider_box = await slider.bounding_box()
            if not slider_box:
                print("[错误] 无法获取滑块边界")
                return False

            start_x = (
                slider_box["x"] + slider_box["width"] / 2 + random.uniform(-1, 1)
            )  # More centered start
            start_y = slider_box["y"] + slider_box["height"] / 2 + random.uniform(-1, 1)

            slide_distance = await self.get_slider_track_width(page, slider)
            if slide_distance is None or slide_distance <= 10:
                print(f"[错误] 最终确定的滑动距离过小或无效({slide_distance})，中止滑动")
                return False

            print(
                f"[信息] 准备滑动，距离: {slide_distance:.2f}px, 起点: ({start_x:.2f}, {start_y:.2f})"
            )

            # 优化：减少鼠标移动步数和等待时间
            if PERFORMANCE_CONFIG.get("reduced_delays"):
                await page.mouse.move(start_x, start_y, steps=random.randint(2, 4))
                await asyncio.sleep(random.uniform(0.04, 0.09))
                await page.mouse.down()
                await asyncio.sleep(random.uniform(0.02, 0.06))
            else:
                await page.mouse.move(start_x, start_y, steps=random.randint(3, 6))
                await asyncio.sleep(random.uniform(0.08, 0.18))
                await page.mouse.down()
                await asyncio.sleep(random.uniform(0.05, 0.12))

            path = self.generate_human_like_path(slide_distance)
            current_mouse_x, current_mouse_y = start_x, start_y

            for _, point in enumerate(path):
                target_x = start_x + point["x"]
                target_y = start_y + point["y"]
                await page.mouse.move(
                    target_x, target_y, steps=random.randint(1, 3)
                )  # Fewer steps per point for smoother aggregate
                await asyncio.sleep(point["delay"])
                current_mouse_x, current_mouse_y = target_x, target_y

            # Ensure final position is accurate
            final_target_x = start_x + slide_distance
            # 优化：减少最终移动和验证等待时间
            if PERFORMANCE_CONFIG.get("reduced_delays"):
                await page.mouse.move(final_target_x, current_mouse_y, steps=random.randint(1, 2))
                await asyncio.sleep(random.uniform(0.04, 0.09))
                await page.mouse.up()
                await asyncio.sleep(random.uniform(0.4, 0.8))  # Wait for verification
            else:
                await page.mouse.move(final_target_x, current_mouse_y, steps=random.randint(2, 4))
                await asyncio.sleep(random.uniform(0.08, 0.18))
                await page.mouse.up()
                await asyncio.sleep(random.uniform(0.7, 1.2))  # Wait for verification
            return True
        except Exception as e:
            print(f"[错误] 执行滑块操作时出错: {e}")
            # traceback.print_exc()
            return False

    async def check_form_errors(self, page: Page) -> Optional[str]:
        try:
            # 首先检查华为特定的设备序列号错误弹窗
            device_error_dialog = await self.check_device_error_dialog(page)
            if device_error_dialog:
                return device_error_dialog

            # 然后检查其他常规错误
            error_selectors = [
                ".yidun_tips.yidun--error .yidun_tips__text >> visible=true",  # Yidun error
                ".error-tips >> visible=true",
                ".wic-device-wrap .wrong >> visible=true",  # Huawei specific input error
                ".el-form-item__error >> visible=true",  # Element UI common error
                ".ant-form-item-explain-error >> visible=true",  # Ant Design common error
                '[class*="error-message"] >> visible=true',
                '[class*="tips-error"] >> visible=true',
            ]
            for selector in error_selectors:
                el = await page.query_selector(selector)
                if el:
                    text = (await el.text_content() or "").strip()
                    if text:
                        return text  # Return first non-empty error found
            return None
        except Exception:
            return "[错误] 检查表单错误时发生未知错误"

    async def check_device_error_dialog(self, page: Page) -> Optional[str]:
        """检查华为设备序列号错误弹窗"""
        try:
            # 检查特定的设备错误弹窗结构
            dialog_selectors = [
                ".s-content-dialog.open-animation.s-show >> visible=true",
                ".s-dialog.s-expressrepair.noDevice >> visible=true",
                ".s-dialog.noDevice >> visible=true",
                ".s-expressrepair.noDevice >> visible=true"
            ]

            for dialog_selector in dialog_selectors:
                dialog = await page.query_selector(dialog_selector)
                if dialog:
                    # 检查弹窗内容
                    dialog_content = await dialog.inner_html()
                    if dialog_content and "SN:" in dialog_content:

                        # 提取序列号信息和错误类型
                        try:
                            title_element = await dialog.query_selector(".s-dialog-title h3")
                            if title_element:
                                title_text = await title_element.inner_text()
                                if "SN:" in title_text:
                                    # 提取SN号码
                                    sn_start = title_text.find("SN:") + 3
                                    sn_end = title_text.find("请输入正确的验证码") if "请输入正确的验证码" in title_text else title_text.find("查不到设备信息")
                                    if sn_end == -1:
                                        sn_end = title_text.find("请输入正确的设备序列号")

                                    if sn_end != -1:
                                        sn_part = title_text[sn_start:sn_end].strip()
                                    else:
                                        sn_part = title_text[sn_start:].strip()

                                    # 根据错误类型返回不同的错误信息
                                    if "请输入正确的验证码" in title_text:
                                        return f"设备序列号无效: SN {sn_part} 请输入正确的验证码"
                                    elif "查不到设备信息" in title_text:
                                        return f"设备序列号无效: SN {sn_part} 查不到设备信息"
                                    elif "请输入正确的设备序列号" in title_text:
                                        return f"设备序列号无效: SN {sn_part} 请输入正确的设备序列号"
                                    else:
                                        return f"设备序列号无效: SN {sn_part}"
                        except Exception:
                            pass

                        # 如果无法提取具体信息，检查通用错误模式
                        if any(keyword in dialog_content for keyword in ["查不到设备信息", "请输入正确的验证码", "请输入正确的设备序列号"]):
                            return "设备序列号无效"

            return None
        except Exception as e:
            print(f"[警告] 检查设备错误弹窗时出错: {e}")
            return None

    async def handle_slider_captcha_with_form(
        self,
        page: Page,
        serial_number: str,
        max_form_attempts: int = 2,  # Reduced default max_form_attempts
    ) -> str:
        for form_attempt in range(max_form_attempts):
            print(
                f"[查询] SN: {serial_number} - 表单提交流程第 {form_attempt + 1}/{max_form_attempts} 次尝试"
            )
            try:
                if await self.check_query_success(page):
                    print(f"[成功] SN: {serial_number} - 表单尝试开始时已成功")
                    return "成功"

                if not await self.fill_form_and_submit(page, serial_number):
                    print(f"[失败] SN: {serial_number} - 表单提交步骤失败")
                    if form_attempt < max_form_attempts - 1:
                        await self._try_reload_page(
                            page, serial_number, "表单提交失败后"
                        )
                        continue
                    return "表单提交失败"

                await asyncio.sleep(
                    random.uniform(0.3, 0.7)
                )  # Wait for immediate feedback

                form_error = await self.check_form_errors(page)
                if form_error:
                    print(f"[错误] SN: {serial_number} - 提交后表单错误: {form_error}")
                    if any(
                        keyword in form_error
                        for keyword in ["序列号", "不存在", "无效", "格式不正确"]
                    ):
                        return "序列号错误"
                    # For other form errors, might retry or reload depending on the error

                MAX_SLIDER_ATTEMPTS_ON_PAGE = 2
                for slider_attempt_on_page in range(MAX_SLIDER_ATTEMPTS_ON_PAGE):
                    if await self.check_query_success(page):
                        return "成功"

                    if not await self.wait_for_slider_appearance(
                        page, timeout=6
                    ):  # Longer wait for slider
                        print(
                            f"[信息] SN: {serial_number} - (滑块尝试 {slider_attempt_on_page+1}/{MAX_SLIDER_ATTEMPTS_ON_PAGE}) 未出现滑块"
                        )
                        if await self.check_query_success(page):
                            return "成功"

                        error_no_slider = await self.check_form_errors(page)
                        if error_no_slider:
                            print(
                                f"[错误] SN:{serial_number} - 无滑块时页面错误: {error_no_slider}"
                            )
                            if any(
                                kw in error_no_slider
                                for kw in ["序列号", "不存在", "无效"]
                            ):
                                return "序列号错误"

                        if slider_attempt_on_page == MAX_SLIDER_ATTEMPTS_ON_PAGE - 1:
                            print(
                                f"[信息] SN: {serial_number} - 当前页面多次未等到滑块，结束当前表单尝试"
                            )
                            break  # Break inner slider loop -> outer form attempt loop (reload)
                        else:
                            print(f"[信息] SN: {serial_number} - 等待1.5秒看滑块是否会加载...")
                            await asyncio.sleep(1.5)
                            continue  # Try finding slider again on this page

                    print(
                        f"[查询] SN: {serial_number} - (滑块尝试 {slider_attempt_on_page+1}/{MAX_SLIDER_ATTEMPTS_ON_PAGE}) 检测到滑块，执行滑动"
                    )
                    if not await self.perform_slider_captcha(page):
                        print(f"[失败] SN: {serial_number} - 滑块操作本身执行失败")
                        # No immediate return, will check success/error below

                    print(f"[查询] SN: {serial_number} - 滑动操作已执行/尝试，检查结果...")
                    if await self.check_slider_success(
                        page, timeout=5
                    ):  # Longer timeout for slider success
                        print(f"[成功] SN: {serial_number} - 滑块验证成功！")

                        # 检查是否出现设备序列号无效弹窗
                        device_error = await self.check_device_error_dialog(page)
                        if device_error:
                            print(f"[错误] SN: {serial_number} - 检测到设备序列号无效弹窗: {device_error}")
                            # 关闭弹窗
                            try:
                                close_button = await page.query_selector(".s-dialog .close, .s-content-dialog .close")
                                if close_button:
                                    await close_button.click()
                                    await asyncio.sleep(0.5)
                            except Exception:
                                pass
                            return "设备序列号无效"

                        return "成功"

                    print(
                        f"[失败] SN: {serial_number} - (滑块尝试 {slider_attempt_on_page+1}/{MAX_SLIDER_ATTEMPTS_ON_PAGE}) 滑块验证失败"
                    )
                    slider_post_error = await self.check_form_errors(
                        page
                    )  # Check for "验证失败", "请重试" etc.
                    if slider_post_error:
                        print(
                            f"[错误] SN: {serial_number} - 滑动后错误提示: {slider_post_error}"
                        )

                    if slider_attempt_on_page < MAX_SLIDER_ATTEMPTS_ON_PAGE - 1:
                        if await self._try_refresh_slider(page, serial_number):
                            continue  # Try slider again on this page
                        else:  # Failed to refresh slider
                            print(
                                f"[信息] SN: {serial_number} - 刷新滑块失败，将结束当前表单尝试"
                            )
                            break  # Break inner slider loop
                    else:
                        print(f"[信息] SN: {serial_number} - 当前页面滑块尝试已达上限")
                        break  # Break inner slider loop

                if await self.check_query_success(page):
                    return "成功"  # Final check after slider loop

                if form_attempt < max_form_attempts - 1:
                    print(f"[信息] SN: {serial_number} - 当前表单尝试的滑块流程结束但未成功")
                    await self._try_reload_page(page, serial_number, "滑块流程失败后")
                # else, it's the last form attempt, loop will naturally end

            except Exception as e:
                print(
                    f"[错误] SN:{serial_number} - 表单流程第{form_attempt+1}次尝试中意外错误: {e}"
                )
                # traceback.print_exc()
                if form_attempt < max_form_attempts - 1:
                    await self._try_reload_page(
                        page, serial_number, f"意外错误 ({str(e)[:30]}) 后"
                    )
                else:
                    return f"最后尝试异常:{str(e)[:50]}"

        print(
            f"[失败] SN: {serial_number} - 已达到最大表单尝试次数 {max_form_attempts}，查询失败"
        )
        final_err = await self.check_form_errors(page)
        if final_err and any(
            keyword in final_err for keyword in ["序列号", "不存在", "无效"]
        ):
            return "序列号错误"
        return "达到最大尝试次数"

    async def _try_reload_page(self, page: Page, serial_number: str, context_msg: str):
        print(f"[信息] SN: {serial_number} - {context_msg}，将刷新页面重试...")
        try:
            # 优化：减少页面重载超时和等待时间
            timeout = 10000 if PERFORMANCE_CONFIG.get("optimized_timeouts") else 15000
            await page.reload(wait_until="domcontentloaded", timeout=timeout)
            if PERFORMANCE_CONFIG.get("reduced_delays"):
                await asyncio.sleep(random.uniform(1.0, 2.0))  # 减少重载后等待时间
            else:
                await asyncio.sleep(random.uniform(2.0, 3.5))  # Longer wait after reload
        except Exception as r_err:
            print(f"[错误] SN: {serial_number} - 刷新页面失败: {r_err}")
            # Consider if further action is needed, e.g., closing context and retrying with new one

    async def _try_refresh_slider(self, page: Page, serial_number: str) -> bool:
        refresh_slider_btn = await page.query_selector(
            self.yidun_refresh_selector + " >> visible=true"
        )
        if refresh_slider_btn:
            print(f"[信息] SN: {serial_number} - 尝试点击滑块刷新按钮...")
            try:
                # 优化：减少刷新按钮点击超时和等待时间
                timeout = 1500 if PERFORMANCE_CONFIG.get("optimized_timeouts") else 2500
                if PERFORMANCE_CONFIG.get("reduced_delays"):
                    await refresh_slider_btn.click(timeout=timeout, delay=random.uniform(25, 50))
                    await asyncio.sleep(random.uniform(0.8, 1.5))  # Wait for new slider images
                else:
                    await refresh_slider_btn.click(timeout=timeout, delay=random.uniform(50, 100))
                    await asyncio.sleep(random.uniform(1.5, 2.5))  # Wait for new slider images
                return True
            except Exception as r_s_err:
                print(f"[错误] SN: {serial_number} - 点击滑块刷新失败: {r_s_err}")
        else:
            print(f"[信息] SN: {serial_number} - 未找到滑块刷新按钮")
        return False

    async def check_slider_success(self, page: Page, timeout: int = 4) -> bool:
        try:
            # 优化：根据性能配置调整超时时间
            if PERFORMANCE_CONFIG.get("optimized_timeouts"):
                timeout = min(timeout, 3)  # 最大3秒超时
            await page.wait_for_selector(
                self.target_success_element_selector,
                state="visible",
                timeout=timeout * 1000,
            )
            return True
        except Exception:
            # 检查是否出现设备序列号无效的错误弹窗
            device_error_dialog = await self.check_device_error_dialog(page)
            if device_error_dialog:
                # 如果检测到设备错误弹窗，也认为滑块验证成功（只是序列号无效）
                return True

            # If target element not found, the crucial check is if the slider is *still* present.
            # If slider is gone, it might be a success or a different type of error handled by check_form_errors.
            if await self.detect_slider_captcha(page):  # Slider still visible
                return False
            # Slider not visible. This could mean success OR the page navigated away to an error page
            # without the main success element. The calling logic (handle_slider_captcha_with_form)
            # will do a more comprehensive check_query_success or check_form_errors.
            # For the purpose of *slider* success, if it's gone, we can't say it failed here.
            # print("Slider success check: Target element not found, slider also not detected. Potential success or other page state.")
            return True  # Tentatively true if slider is gone, main logic will confirm overall success.

    async def check_query_success(self, page: Page) -> bool:
        try:
            if await page.query_selector(
                self.target_success_element_selector + " >> visible=true"
            ):
                return True
            # Consider adding URL checks only if they are very reliable for success
            # if any(k in page.url for k in ["result", "success", "warranty-detail"]): return True
            return False
        except Exception:
            return False

    async def _get_text_content(self, element: Optional[ElementHandle]) -> str:
        if not element:
            return ""
        try:
            return (await element.text_content() or "").strip()
        except Exception:  # Handle cases where element might become detached
            return ""

    async def extract_warranty_information(self, page: Page) -> Dict[str, Any]:
        data: Dict[str, Any] = {
            "product_info": {},
            "electronic_warranty": {},
            "mail_in_repair_logistics": {},
        }
        # 优化：减少信息提取前的等待时间
        if PERFORMANCE_CONFIG.get("reduced_delays"):
            await asyncio.sleep(random.uniform(0.1, 0.2))
        else:
            await asyncio.sleep(random.uniform(0.2, 0.4))
        try:
            main_c = await page.query_selector(
                self.target_success_element_selector + " >> visible=true"
            )
            if not main_c:
                print("[错误] 提取信息失败：未找到主内容容器")
                return data

            # Product Info
            data["product_info"]["title"] = await self._get_text_content(
                await main_c.query_selector(
                    "h2.guarantee-invalid-title, h2.product-title"
                )
            )
            data["product_info"]["name"] = await self._get_text_content(
                await main_c.query_selector(
                    "div.g-i-right span.p1, div.product-name span"
                )
            )

            # SN and Region with more robust text matching
            items_div = await main_c.query_selector("div.g-i-right div.effective")
            if items_div:
                all_items = await items_div.query_selector_all(
                    "div:has(span.in-span-label)"
                )
                for item_cont in all_items:
                    label_el = await item_cont.query_selector("span.in-span-label")
                    value_el = await item_cont.query_selector("span.in-span-value")
                    label_text = await self._get_text_content(label_el)
                    value_text = await self._get_text_content(value_el)

                    if "序列号" in label_text or "Serial Number" in label_text:
                        data["product_info"]["serial_number"] = value_text
                    elif "适用区域" in label_text or "Applicable Region" in label_text:
                        data["product_info"]["region"] = value_text

            async def extract_card_data(card_el: ElementHandle) -> Dict[str, str]:
                d_card = {}
                els = await card_el.query_selector_all(
                    "div.elements-wrap > div.element, div.elements-wrap div.element-item"
                )
                for el_i in els:
                    k_el = await el_i.query_selector(
                        "div.element-key, span.element-key"
                    )
                    v_el = await el_i.query_selector(
                        "div.element-value, span.element-value"
                    )
                    key_t = (
                        (await self._get_text_content(k_el))
                        .replace("：", "")
                        .replace(":", "")
                        .strip()
                    )
                    if key_t:
                        d_card[key_t] = await self._get_text_content(v_el)
                return d_card

            # Electronic Warranty Card
            ew_c = await page.query_selector(
                "li.card:has(div.base-title:text-matches('电子三包凭证|Electronic Warranty', 'i')) >> visible=true"
            )
            if ew_c:
                data["electronic_warranty"]["title"] = await self._get_text_content(
                    await ew_c.query_selector("div.base-title")
                )
                data["electronic_warranty"].update(await extract_card_data(ew_c))
                det_link_el = await ew_c.query_selector(
                    "div.view-right-detail[iframe-url]"
                )
                if det_link_el:
                    data["electronic_warranty"]["details_url"] = (
                        await det_link_el.get_attribute("iframe-url") or ""
                    )

            # Mail-in Repair Logistics Card
            mr_c = await page.query_selector(
                "li.card:has(div.base-title:text-matches('寄修双向免物流费|Mail-in Repair', 'i')) >> visible=true"
            )
            if mr_c:
                data["mail_in_repair_logistics"]["title"] = (
                    await self._get_text_content(
                        await mr_c.query_selector("div.base-title")
                    )
                )
                data["mail_in_repair_logistics"].update(await extract_card_data(mr_c))
        except Exception as e:
            print(f"[错误] 提取保修信息时发生错误: {e}")
            # traceback.print_exc()
        return {
            k: v
            for k, v in data.items()
            if isinstance(v, str) or (isinstance(v, dict) and any(v.values()))
        }


# 默认并发数量，将在运行时根据用户选择进行调整
MAX_CONCURRENT_QUERIES = 4  # 提高默认并发数
PROXY_API_KEY = "0LM61IPB"  # Default key, can be customized by user
USE_PROXY = False

# 性能优化配置
PERFORMANCE_CONFIG = {
    "fast_mode": True,  # 启用快速模式
    "reduced_delays": True,  # 减少延迟
    "optimized_timeouts": True,  # 优化超时设置
    "browser_pool_size": 2,  # 浏览器池大小
}

# 优化：根据性能配置选择不同的反检测脚本
FAST_EVASION_SCRIPT = """
(() => {
    // 快速模式：只包含最基本的反检测
    if (navigator.webdriver) {
        try { Object.defineProperty(navigator, 'webdriver', { get: () => false }); } catch (e) {}
    }
    if (!window.chrome) window.chrome = { runtime: {} };
    const KEYS_TO_DELETE = ['_phantom', '__nightmare', '_selenium', 'callPhantom', 'callSelenium'];
    KEYS_TO_DELETE.forEach(key => { try { delete window[key]; } catch(e) {} });
})();
"""

EVASION_SCRIPT_CONTENT = """
(() => {
    // --- navigator.webdriver ---
    if (navigator.webdriver || typeof navigator.webdriver === 'undefined') { // Check undefined too
        try { Object.defineProperty(navigator, 'webdriver', { get: () => false }); } catch (e) {}
    }

    // --- navigator.plugins ---
    const pluginsData = [
        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format', MimeTypes: [{type: "application/pdf", suffixes: "pdf", description: "Portable Document Format"}]},
        { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '', MimeTypes: [{type: "application/pdf", suffixes: "pdf", description: ""}]},
        { name: 'Native Client', filename: 'internal-nacl-plugin', description: '', MimeTypes: [{type: "application/x-nacl", suffixes: "", description: "Native Client Executable"}, {type: "application/x-pnacl", suffixes: "", description: "Portable Native Client Executable"}]}
    ];
    pluginsData.forEach(p => p.length = p.MimeTypes.length); // Set length property for each plugin based on its MimeTypes

    if (typeof navigator.plugins === 'object' || typeof navigator.plugins === 'undefined') {
        const pluginsProxy = new Proxy(pluginsData, {
            get(target, prop) {
                if (prop === 'length') return target.length;
                if (typeof prop === 'string' && !isNaN(parseInt(prop))) return target[parseInt(prop)];
                if (prop === 'item') return (index) => target[index];
                if (prop === 'namedItem') return (name) => target.find(p => p.name === name);
                if (typeof PluginArray !== 'undefined' && PluginArray.prototype.hasOwnProperty(prop)) { // Inherit from PluginArray
                    return PluginArray.prototype[prop];
                }
                return undefined;
            },
            ownKeys(target) { return Array.from({length: target.length}, (_, i) => String(i)); },
            getOwnPropertyDescriptor(target, prop) {
                if (typeof prop === 'string' && !isNaN(parseInt(prop)) && parseInt(prop) < target.length) {
                    return { value: target[parseInt(prop)], writable: false, enumerable: true, configurable: true };
                } return undefined;
            },
            [Symbol.iterator]: function* () { for (let i = 0; i < pluginsData.length; i++) yield pluginsData[i]; }
        });
        try { Object.defineProperty(navigator, 'plugins', { get: () => pluginsProxy }); } catch (e) {}
    }

    // --- navigator.mimeTypes ---
    const mimeTypesData = [];
    pluginsData.forEach(plugin => {
        plugin.MimeTypes.forEach(mime => {
            mimeTypesData.push({...mime, enabledPlugin: plugin });
        });
    });
    if (typeof navigator.mimeTypes === 'object' || typeof navigator.mimeTypes === 'undefined') {
        const mimeTypesProxy = new Proxy(mimeTypesData, {
            get(target, prop) {
                if (prop === 'length') return target.length;
                if (typeof prop === 'string' && !isNaN(parseInt(prop))) return target[parseInt(prop)];
                if (prop === 'item') return (index) => target[index];
                if (prop === 'namedItem') return (name) => target.find(m => m.type === name);
                 if (typeof MimeTypeArray !== 'undefined' && MimeTypeArray.prototype.hasOwnProperty(prop)) { // Inherit
                    return MimeTypeArray.prototype[prop];
                }
                return undefined;
            },
            ownKeys(target) { return Array.from({length: target.length}, (_, i) => String(i)); },
            getOwnPropertyDescriptor(target, prop) {
                if (typeof prop === 'string' && !isNaN(parseInt(prop)) && parseInt(prop) < target.length) {
                    return { value: target[parseInt(prop)], writable: false, enumerable: true, configurable: true };
                } return undefined;
            },
            [Symbol.iterator]: function* () { for (let i = 0; i < mimeTypesData.length; i++) yield mimeTypesData[i];}
        });
        try { Object.defineProperty(navigator, 'mimeTypes', { get: () => mimeTypesProxy }); } catch (e) {}
    }

    // --- navigator.languages ---
    if (navigator.languages || typeof navigator.languages === 'undefined') {
        try { Object.defineProperty(navigator, 'languages', { get: () => ['zh-CN', 'zh', 'en-US', 'en'] }); } catch (e) {}
    }
    if (navigator.language || typeof navigator.language === 'undefined') {
         try { Object.defineProperty(navigator, 'language', { get: () => 'zh-CN' }); } catch (e) {}
    }


    // --- WebGL ---
    try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl) {
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            const vendorKey = debugInfo ? debugInfo.UNMASKED_VENDOR_WEBGL : null;
            const rendererKey = debugInfo ? debugInfo.UNMASKED_RENDERER_WEBGL : null;

            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === vendorKey) return 'Google Inc. (Intel)';
                if (parameter === rendererKey) return 'ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)';
                return originalGetParameter.call(this, parameter);
            };
            // Further WebGL spoofing can be added here if needed (e.g., getShaderPrecisionFormat)
        }
    } catch (e) { /* console.warn('WebGL spoofing failed:', e); */ }

    // --- window.chrome ---
    if (typeof window.chrome === 'undefined') { window.chrome = {}; }
    if (typeof window.chrome.runtime === 'undefined') { window.chrome.runtime = {}; }
    // Add properties if site checks for them, e.g. window.chrome.csi
    if (typeof window.chrome.csi === 'undefined') { window.chrome.csi = function() {}; }


    // --- Permissions API ---
    if (navigator.permissions && navigator.permissions.query) {
        const originalQuery = navigator.permissions.query;
        navigator.permissions.query = (parameters) => {
            if (parameters.name === 'notifications') {
                return Promise.resolve({ state: Notification.permission === 'default' ? 'prompt' : Notification.permission });
            }
            return originalQuery.apply(navigator.permissions, [parameters]);
        };
        if (typeof Notification !== 'undefined' && 'permission' in Notification) {
           try { Object.defineProperty(Notification, 'permission', { get: () => 'default' }); } catch(e){}
        }
    }

    // --- Screen properties (often checked) ---
    if(typeof screen !== 'undefined'){
        try {
            Object.defineProperty(screen, 'availTop', { get: () => 0 });
            Object.defineProperty(screen, 'availLeft', { get: () => 0 });
            // availWidth/Height should ideally match viewport but can be fixed
            if (screen.availWidth < 1024) Object.defineProperty(screen, 'availWidth', { get: () => 1920 });
            if (screen.availHeight < 700) Object.defineProperty(screen, 'availHeight', { get: () => 1040 }); // 1080 - typical taskbar
            if (screen.width < 1024) Object.defineProperty(screen, 'width', { get: () => 1920 });
            if (screen.height < 700) Object.defineProperty(screen, 'height', { get: () => 1080 });
            if (screen.colorDepth < 24) Object.defineProperty(screen, 'colorDepth', { get: () => 24 });
            if (screen.pixelDepth < 24) Object.defineProperty(screen, 'pixelDepth', { get: () => 24 });
        } catch(e){}
    }

    // --- Remove specific automation flags from window object ---
    const KEYS_TO_DELETE = [
        '_phantom', '__nightmare', '_selenium', 'callPhantom', 'callSelenium', '_Selenium_IDE_Recorder',
        '__driver_evaluate', '__webdriver_evaluate', '__selenium_evaluate', '__fxdriver_evaluate',
        '__driver_unwrapped', '__webdriver_unwrapped', '__selenium_unwrapped', '__fxdriver_unwrapped',
        '_selenium_evaluate', 'webdriver', // Some sites might set window.webdriver
    ];
    KEYS_TO_DELETE.forEach(key => {
        try { delete window[key]; } catch(e) {}
    });

    // --- Spoof common Chrome properties ---
    if (!window.chrome) window.chrome = {};
    if (!window.chrome.runtime) window.chrome.runtime = {};
    // If specific properties are checked, like `loadTimes`, `csi`
    if (typeof window.chrome.loadTimes !== 'function') {
        window.chrome.loadTimes = function() { return {}; };
    }
    if (typeof window.chrome.csi !== 'function') {
        window.chrome.csi = function() { return {
            onloadT: Date.now() - Math.random() * 1000, // Simulate some load time
            startE: Date.now() - Math.random() * 2000,
            pageT: Math.random() * 100,
            tran: Math.random() * 10
        }; };
    }


})();
"""


async def process_single_huawei_sn(
    browser: Browser,
    proxy_manager: Optional[ProxyManager],
    captcha_handler: SliderCaptchaHandler,
    serial_number: str,
    semaphore: asyncio.Semaphore,
) -> Dict[str, Any]:
    async with semaphore:
        page: Optional[Page] = None
        context: Optional[BrowserContext] = None
        start_time = time.time()
        proxy_server_used_for_request: Optional[str] = None  # For logging
        query_result_data: Dict[str, Any] = {
            "serial_number": serial_number,
            "status": "初始化失败",
            "data": None,
            "duration_seconds": 0.0,
            "proxy_used": "未启用",
            "error_message": None,
        }
        print(f"\n[查询] 开始处理华为设备查询 - SN: {serial_number}")
        try:
            context_options: Dict[str, Any] = {
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "viewport": {
                    "width": random.randint(1366, 1920),
                    "height": random.randint(768, 1080),
                },
                "java_script_enabled": True,
                "bypass_csp": True,
                "locale": "zh-CN",
                "timezone_id": "Asia/Shanghai",
                "permissions": [
                    "geolocation",
                    "notifications",
                ],  # Grant common permissions
                "geolocation": {
                    "longitude": 121.4737,
                    "latitude": 31.2304,
                    "accuracy": random.randint(10, 50),
                },  # Shanghai coords
                "device_scale_factor": 1,  # Common desktop setting
            }
            if proxy_manager and USE_PROXY:  # Check USE_PROXY flag
                proxy_config_dict = await proxy_manager.get_proxy_config()
                if proxy_config_dict and proxy_config_dict.get("server"):
                    proxy_server_used_for_request = proxy_config_dict["server"]
                    context_options["proxy"] = (
                        proxy_config_dict  # {"server": "http://IP:PORT"}
                    )
                    query_result_data["proxy_used"] = proxy_server_used_for_request
                else:
                    query_result_data["proxy_used"] = "获取失败"
                    if USE_PROXY:  # If proxy was intended but failed
                        print(f"[警告] SN: {serial_number} - 代理获取失败，将无代理运行")
                        # Optionally, you could decide to not proceed without proxy:
                        # query_result_data["status"] = "代理获取失败"
                        # return query_result_data

            context = await browser.new_context(**context_options)
            # 优化：根据性能配置选择反检测脚本
            if PERFORMANCE_CONFIG.get("fast_mode"):
                await context.add_init_script(script=FAST_EVASION_SCRIPT)
            else:
                await context.add_init_script(script=EVASION_SCRIPT_CONTENT)
            page = await context.new_page()

            # Optional: Stealth plugin is available for playwright-python-stealth, more comprehensive
            # from playwright_stealth import stealth_async
            # await stealth_async(page)

            # Block common trackers that might also do bot detection (be careful not to block essential resources)
            # await page.route("**/*google-analytics.com/**", lambda route: route.abort())
            # await page.route("**/*doubleclick.net/**", lambda route: route.abort())

            url = "https://consumer.huawei.com/cn/support/warranty-query/"
            navigation_successful = False
            try:
                # 优化：根据性能配置和代理状态调整导航超时
                if PERFORMANCE_CONFIG.get("optimized_timeouts"):
                    nav_timeout = 20000 if proxy_server_used_for_request else 15000
                else:
                    nav_timeout = 35000 if proxy_server_used_for_request else 25000
                print(f"[查询] SN: {serial_number} - 导航至 {url} (超时: {nav_timeout/1000}s)")
                await page.goto(url, wait_until="domcontentloaded", timeout=nav_timeout)
                navigation_successful = True
            except Exception as nav_exc:
                proxy_info = (
                    f"(代理:{proxy_server_used_for_request})"
                    if proxy_server_used_for_request
                    else "(无代理)"
                )
                print(f"[错误] SN:{serial_number} - 导航失败 {proxy_info}: {nav_exc}")
                query_result_data["status"] = "导航失败"
                query_result_data["error_message"] = f"导航: {str(nav_exc)[:100]}"
                if context:
                    await context.close()  # Close context on critical failure
                return query_result_data  # Early exit

            if (
                USE_PROXY
                and proxy_manager
                and proxy_server_used_for_request
                and navigation_successful
            ):
                await proxy_manager.increment_proxy_usage(proxy_server_used_for_request)

            try:
                # More robust cookie button handling
                cookie_selectors = ["#cookieDesc a.button-default", "button:has-text('接受'), button:has-text('Accept')"]
                cookie_clicked = False
                for sel in cookie_selectors:
                    try:
                        # 优化：减少Cookie按钮等待超时
                        timeout = 1500 if PERFORMANCE_CONFIG.get("optimized_timeouts") else 2500
                        cookie_btn = await page.wait_for_selector(sel, state="visible", timeout=timeout)
                        if cookie_btn:
                            print(f"[信息] SN: {serial_number} - 找到Cookie按钮 ({sel})，尝试点击...")
                            # 优化：减少点击延迟和等待时间
                            if PERFORMANCE_CONFIG.get("reduced_delays"):
                                await cookie_btn.click(timeout=1500, delay=random.uniform(25, 75))
                                await asyncio.sleep(random.uniform(0.15, 0.35))
                            else:
                                await cookie_btn.click(timeout=2000, delay=random.uniform(50, 150))
                                await asyncio.sleep(random.uniform(0.3, 0.7))
                            cookie_clicked = True
                            break
                    except Exception: # TimeoutError or other errors if element not found/visible in time
                        # print(f"SN: {serial_number} - Cookie按钮选择器 '{sel}' 未在超时内找到或可见。")
                        pass # 继续尝试下一个选择器

                if not cookie_clicked:
                    print(f"[信息] SN: {serial_number} - 未找到或未点击任何Cookie按钮")
            except Exception as cookie_e:
                print(f"[警告] SN: {serial_number} - Cookie按钮处理跳过/失败: {cookie_e}")

            status = await captcha_handler.handle_slider_captcha_with_form(
                page, serial_number, max_form_attempts=2
            )
            query_result_data["status"] = status

            if status == "成功":
                print(f"[成功] SN: {serial_number} - 查询成功，提取信息...")
                # 优化：减少信息提取前的等待时间
                if PERFORMANCE_CONFIG.get("reduced_delays"):
                    await asyncio.sleep(random.uniform(0.1, 0.25))
                else:
                    await asyncio.sleep(random.uniform(0.2, 0.5))
                extracted_data = await captcha_handler.extract_warranty_information(page)
                if extracted_data:
                    query_result_data["data"] = extracted_data
                else:
                    query_result_data["status"] = (
                        "成功但无数据"  # More specific status
                    )
                    print(f"[警告] SN: {serial_number} - 查询成功但未能提取到详细数据")
            elif status == "序列号错误":
                print(f"[错误] SN: {serial_number} - 状态指示序列号错误: {status}")
                query_result_data["error_message"] = "设备序列号错误，请检查输入的序列号是否正确"
            elif status == "设备序列号无效":
                print(f"[错误] SN: {serial_number} - 状态指示设备序列号无效: {status}")
                query_result_data["error_message"] = "设备序列号无效，请检查输入的序列号是否正确"
            else:
                print(f"[失败] SN: {serial_number} - 未成功，状态: {status}")

        except Exception as e:
            proxy_info = (
                f"(代理:{proxy_server_used_for_request})"
                if proxy_server_used_for_request
                else "(无代理)"
            )
            error_msg = f"[错误] SN {serial_number} {proxy_info} 严重错误: {e}"
            print(error_msg)
            # traceback.print_exc() # Uncomment for full traceback during debugging
            query_result_data["status"] = "严重错误"
            query_result_data["error_message"] = str(e)[
                :150
            ]  # Keep error message concise
        finally:
            print(f"[清理] SN: {serial_number} - 清理资源...")
            if page and not page.is_closed():
                await page.close()
                print(f"[清理] SN: {serial_number} - 页面已关闭")
            if context:
                await context.close()
                print(f"[清理] SN: {serial_number} - 浏览器上下文已关闭")
            query_result_data["duration_seconds"] = round(time.time() - start_time, 2)
            print(
                f"[完成] SN: {serial_number} - 处理完成, 耗时: {query_result_data['duration_seconds']:.2f}s, 状态: {query_result_data['status']}"
            )
            return query_result_data


async def main_batch_huawei(custom_proxy_key: Optional[str] = None):
    global USE_PROXY, MAX_CONCURRENT_QUERIES, PROXY_API_KEY
    serial_numbers: List[str] = []

    # 如果提供了自定义代理密钥，则使用它
    if custom_proxy_key:
        PROXY_API_KEY = custom_proxy_key

    # --- User Input ---

    # 询问性能模式
    print("[配置] 请选择性能模式:")
    print("1. 快速模式 (推荐) - 优化所有等待时间，提升查询速度")
    print("2. 标准模式 - 保持原有的等待时间，稳定性最佳")

    performance_choice = input("请选择性能模式 (1-2) [默认为1，快速模式]: ").strip() or "1"

    if performance_choice == "1":
        print("[配置] 已启用快速模式 - 所有等待时间已优化")
        # 快速模式已经是默认配置
    elif performance_choice == "2":
        print("[配置] 已启用标准模式 - 保持原有等待时间")
        PERFORMANCE_CONFIG.update({
            "fast_mode": False,
            "reduced_delays": False,
            "optimized_timeouts": False,
        })
    else:
        print("[警告] 无效选择，使用默认快速模式")

    # 询问并发查询数量
    print("\n[配置] 请选择并发查询数量:")
    print("1. 2个并发 (稳定性最佳)")
    print("2. 4个并发 (推荐，平衡性能和稳定性)")
    print("3. 8个并发 (较高性能)")
    print("4. 16个并发 (高性能)")
    print("5. 32个并发 (最高性能，可能不稳定)")
    print("6. 自定义数量")

    concurrent_choice = input("请选择 (1-6) [默认为2，即4个并发]: ").strip() or "2"

    concurrent_options = {
        "1": 2,
        "2": 4,
        "3": 8,
        "4": 16,
        "5": 32
    }

    if concurrent_choice in concurrent_options:
        MAX_CONCURRENT_QUERIES = concurrent_options[concurrent_choice]
        print(f"[配置] 已设置并发数量为: {MAX_CONCURRENT_QUERIES}")
    elif concurrent_choice == "6":
        while True:
            try:
                custom_concurrent = int(input("请输入自定义并发数量 (1-32): "))
                if 1 <= custom_concurrent <= 32:
                    MAX_CONCURRENT_QUERIES = custom_concurrent
                    print(f"[配置] 已设置自定义并发数量为: {MAX_CONCURRENT_QUERIES}")
                    break
                else:
                    print("[错误] 并发数量必须在1-32之间，请重新输入")
            except ValueError:
                print("[错误] 请输入有效的数字")
    else:
        print("[警告] 无效选择，使用默认值4个并发")
        MAX_CONCURRENT_QUERIES = 4

    # 询问是否使用代理
    use_proxy_input = (
        input("是否启用代理进行查询? (y/N) [默认为N，不启用]: ").strip().lower()
    )
    if use_proxy_input == "y":
        USE_PROXY = True
        print("[配置] 代理已启用")
    else:
        USE_PROXY = False
        print("[配置] 代理未启用")

    choice = (
        input("选择序列号输入方式: (1) 手动输入 (2) 从Excel文件读取 [默认1]: ")
        .strip()
        .lower()
        or "1"
    )

    if choice == "1":
        serial_numbers_input = input(
            "请输入华为设备序列号 (多个请用英文逗号或空格分隔): "
        )
        serial_numbers = [
            sn.strip()
            for sn in serial_numbers_input.replace(",", " ").split()
            if sn.strip()
        ]
    elif choice == "2":
        excel_path = input(
            "请输入Excel文件路径 (例如: D:\\data\\serials.xlsx): "
        ).strip()
        try:
            df = pd.read_excel(excel_path, engine="openpyxl", header=0)

            temp_serial_numbers: List[str] = []
            if not df.empty:
                user_column_name = input(
                    f"Excel中包含序列号的列标题是什么? (例如: 串码信息, 如果是第一列可直接回车): "
                ).strip()

                if user_column_name and user_column_name in df.columns:
                    print(f"[信息] 将从列 '{user_column_name}' 读取序列号")
                    temp_serial_numbers = (
                        df[user_column_name].astype(str).dropna().tolist()
                    )
                elif not user_column_name and len(df.columns) > 0 and df.columns[0]:
                    default_col_name = df.columns[0]
                    print(
                        f"[信息] 未指定列名，将从检测到的第一列 '{default_col_name}' 读取序列号"
                    )
                    temp_serial_numbers = df.iloc[:, 0].astype(str).dropna().tolist()
                elif not df.empty:
                    print(
                        f"[信息] 未指定列名，且第一列无明确标题或指定列名无效，将尝试从第一列数据读取"
                    )
                    temp_serial_numbers = df.iloc[:, 0].astype(str).dropna().tolist()
                else:
                    print("[错误] 无法确定从哪一列读取序列号，或Excel列为空")
            else:
                print("[错误] Excel文件为空")

            serial_numbers = [sn.strip() for sn in temp_serial_numbers if sn.strip()]
            if serial_numbers:
                print(
                    f"[信息] 从Excel文件 '{excel_path}' 读取到 {len(serial_numbers)} 个序列号"
                )
        except FileNotFoundError:
            print(f"[错误] Excel文件 '{excel_path}' 未找到")
        except Exception as e:
            print(f"[错误] 读取Excel文件 '{excel_path}' 时出错: {e}")
            traceback.print_exc()
    else:
        print("[错误] 无效的选择")

    if not serial_numbers:
        print("[错误] 未获取到有效序列号，程序退出")
        return

    print("\n" + "="*50)
    print("[配置] 华为设备查询配置摘要:")
    print(f"序列号数量: {len(serial_numbers)}")
    print(f"并发查询数: {MAX_CONCURRENT_QUERIES}")
    print(f"性能模式: {'快速模式' if PERFORMANCE_CONFIG.get('fast_mode') else '标准模式'}")
    if USE_PROXY:
        print("代理状态: 已启用")
    else:
        print("代理状态: 未启用")
    print("="*50)

    # 根据配置给出性能提示
    if PERFORMANCE_CONFIG.get('fast_mode'):
        print("[信息] 快速模式已启用，查询速度已优化")
    else:
        print("[信息] 标准模式已启用，保持原有稳定性")

    if MAX_CONCURRENT_QUERIES <= 2:
        print("[信息] 当前使用低并发模式，查询速度较慢但稳定性最佳")
    elif MAX_CONCURRENT_QUERIES <= 8:
        print("[信息] 当前使用中等并发模式，平衡了速度和稳定性")
    else:
        print("[信息] 当前使用高并发模式，查询速度快但可能遇到更多验证码或限制")

    print("\n[查询] 开始华为设备查询...")
    all_results = []
    proxy_manager_instance: Optional[ProxyManager] = None
    if USE_PROXY:
        proxy_manager_instance = ProxyManager(
            api_url="https://share.proxy.qg.net/get",  # Replace if different
            max_usage_per_ip=random.randint(3, 5),  # Randomize slightly
            proxy_key=PROXY_API_KEY,
        )
    captcha_handler = SliderCaptchaHandler()

    async with async_playwright() as p:
        browser: Optional[Browser] = None
        try:
            # 优化：根据性能配置简化启动参数
            if PERFORMANCE_CONFIG.get("fast_mode"):
                # 快速模式：只保留必要的启动参数
                launch_args = [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation",
                    "--disable-extensions",
                    "--disable-default-apps",
                    "--window-size=1366,768",
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--mute-audio",
                ]
            else:
                # 完整模式：保留所有启动参数
                launch_args = [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation",
                    "--disable-features=VizDisplayCompositor,WebRtcHideLocalIpsWithMdns,ConsolidatedMovementXY",
                    "--ignore-certificate-errors",
                    "--ignore-ssl-errors",
                    "--ignore-certificate-errors-spki-list",
                    "--disable-extensions",
                    "--disable-default-apps",
                    "--disable-component-extensions-with-background-pages",
                    "--window-size=1366,768",
                    "--force-color-profile=srgb",
                    "--disable-popup-blocking",
                    "--enable-features=NetworkService,NetworkServiceInProcess",
                    "--enable-automation",
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-background-networking",
                    "--disable-sync",
                    "--metrics-recording-only",
                    "--disable-component-update",
                    "--hide-scrollbars",
                    "--mute-audio",
                ]
            browser = await p.chromium.launch(
                headless=True,  # KEY SETTING TO TEST THE FIXES
                args=list(set(launch_args)),  # Ensure unique args
                # channel="chrome", # Try using system chrome if playwright's chromium fails
                # executable_path= "C:/Program Files/Google/Chrome/Application/chrome.exe" # if channel="chrome"
            )
            semaphore = asyncio.Semaphore(MAX_CONCURRENT_QUERIES)
            tasks = [
                process_single_huawei_sn(
                    browser, proxy_manager_instance, captcha_handler, sn, semaphore
                )
                for sn in serial_numbers
            ]
            batch_start_time = time.time()
            results_from_gather = await asyncio.gather(
                *tasks, return_exceptions=True
            )  # return_exceptions=True to see individual errors
            batch_end_time = time.time()

            for i, res_or_exc in enumerate(results_from_gather):
                if isinstance(res_or_exc, Exception):
                    print(
                        f"[错误] SN: {serial_numbers[i]} - 任务执行时抛出未捕获异常: {res_or_exc}"
                    )
                    # traceback.print_exception(type(res_or_exc), res_or_exc, res_or_exc.__traceback__) # Detailed traceback
                    all_results.append(
                        {
                            "serial_number": serial_numbers[i],
                            "status": "任务异常",
                            "error_message": str(res_or_exc)[:200],
                            "data": None,
                            "duration_seconds": 0,
                            "proxy_used": "N/A",
                        }
                    )
                else:
                    all_results.append(res_or_exc)

            total_time = batch_end_time - batch_start_time
            avg_time_per_query = total_time / len(serial_numbers) if serial_numbers else 0
            theoretical_sequential_time = sum(res.get('duration_seconds', 0) for res in all_results if isinstance(res, dict))
            efficiency = (theoretical_sequential_time / total_time) if total_time > 0 else 0

            print(f"\n[完成] --- 华为设备查询全部完成 ---")
            print(f"总耗时: {total_time:.2f}秒")
            print(f"并发数: {MAX_CONCURRENT_QUERIES}")
            print(f"平均每个查询耗时: {avg_time_per_query:.2f}秒")
            print(f"并发效率: {efficiency:.1f}x (相对于顺序执行)")
            print("-" * 50)
            successful_queries = 0
            for res in all_results:
                print("\n----------------------------------------")
                print(
                    f"序列号: {res['serial_number']}\n状态: {res['status']}\n耗时: {res['duration_seconds']:.2f}s\n代理: {res['proxy_used']}"
                )
                if res.get("error_message"):
                    print(f"错误信息: {res['error_message']}")
                if res.get("data"):
                    # print("详细信息:")
                    #print(json.dumps(res["data"], indent=2, ensure_ascii=False))
                    if "成功" in res["status"]:
                        successful_queries += 1
                elif "成功" in res["status"] and not res.get("data"):
                    print(
                        "查询成功但未能提取详细结果"
                    )  # e.g. status "成功但无数据"
                    successful_queries += 1
                print("----------------------------------------")
                if res.get("data") and res["data"].get('product_info'):
                    result = res["data"].get('product_info')
                    data = {"产品信息": {}, "查询序列号": result.get('serial_number')}
                    data["产品信息"]["产品名称"] = result.get('name')
                    data["产品信息"]["显示序列号"] = result.get('serial_number')
                    data["产品信息"]["适用范围"] = result.get('region')
                    print("详细信息:")
                    print(json.dumps(data, indent=2, ensure_ascii=False))

            print(f"\n[统计] 总计查询: {len(all_results)}, 成功提取数据: {successful_queries}")

            # if all_results:
            #     output_filename = (
            #         f"huawei_query_results_{time.strftime('%Y%m%d_%H%M%S')}.json"
            #     )
            #     try:
            #         with open(output_filename, "w", encoding="utf-8") as f:
            #             json.dump(all_results, f, indent=2, ensure_ascii=False)
            #         print(f"\n结果已保存到 {output_filename}")
            #     except Exception as write_e:
            #         print(f"保存结果到JSON文件失败: {write_e}")
            #         print(
            #             "原始结果数据:\n",
            #             json.dumps(all_results, indent=2, ensure_ascii=False),
            #         )

        except Exception as e:
            print(f"[错误] 主逻辑严重错误:{e}")
            traceback.print_exc()
        finally:
            print("\n[清理] 正在关闭资源...")
            if proxy_manager_instance:
                await proxy_manager_instance.close_aio_session_proxy()
                print("[清理] 代理管理器已清理")
            await captcha_handler.close_aio_session_img()
            print("[清理] 验证码处理器已清理")
            if browser and browser.is_connected():  # Check if connected before closing
                await browser.close()
                print("[清理] 浏览器已关闭")
            print("[完成] 华为设备查询操作完成")


if __name__ == "__main__":
    # import sys # For Windows event loop policy if needed
    # if sys.platform == "win32" and sys.version_info >= (3, 8, 0):
    #    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main_batch_huawei())

# --- END OF FILE huawei_new2_update_2.py ---
