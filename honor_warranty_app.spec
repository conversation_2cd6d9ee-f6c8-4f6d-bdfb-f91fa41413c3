# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['honor_warranty_app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('honor_logo.ico', '.'),
        ('honor_logo.png', '.'),
        ('warning_icon.png', '.'),
        ('VC_redist.x64.exe', '.'),
        ('disclaimer_txt.py', '.'),
        ('honor_warranty_querier.py', '.'),
        ('huawei_new2_update_3.py', '.'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'pandas',
        'ddddocr',
        'aiohttp',
        'openpyxl',
        'asyncio',
        'traceback',
        'io',
        'base64',
        'random',
        'time',
        'json',
        'urllib.parse',
        'subprocess',
        'playwright',
        'playwright.async_api',
        'playwright.sync_api',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 不排除任何模块，确保所有依赖都被包含
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='华为荣耀设备保修查询工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='honor_logo.ico',  # 设置应用程序图标
    version_info=None,
)
